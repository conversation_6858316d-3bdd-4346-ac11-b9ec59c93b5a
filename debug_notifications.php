<?php
/**
 * Quick debug script to check notification data directly
 */

session_start();
require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/staff_panel_functions.php';

// Check if user is logged in as staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    echo "<h1>Please log in as staff first</h1>";
    echo "<a href='/flix-php/auth/login.php'>Login</a>";
    exit;
}

$staffId = $_SESSION['user_id'];

echo "<h1>Notification Debug</h1>";
echo "<p><strong>Staff ID:</strong> " . htmlspecialchars($staffId) . "</p>";
echo "<p><strong>Staff Name:</strong> " . htmlspecialchars($_SESSION['user_name']) . "</p>";
echo "<hr>";

// Check if notifications table exists
try {
    $tableExists = $database->fetch("SHOW TABLES LIKE 'notifications'");
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ Notifications table does not exist!</p>";
        exit;
    }
    echo "<p style='color: green;'>✅ Notifications table exists</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking table: " . $e->getMessage() . "</p>";
    exit;
}

// Get direct count from database
try {
    $directCount = $database->fetch("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0", [$staffId]);
    $totalCount = $database->fetch("SELECT COUNT(*) as count FROM notifications WHERE user_id = ?", [$staffId]);
    
    echo "<h2>Direct Database Query Results:</h2>";
    echo "<p><strong>Total notifications:</strong> " . $totalCount['count'] . "</p>";
    echo "<p><strong>Unread notifications:</strong> " . $directCount['count'] . "</p>";
    
    if ($directCount['count'] > 0) {
        echo "<p style='color: green; font-size: 18px;'>🔔 Badge should show: " . $directCount['count'] . "</p>";
    } else {
        echo "<p style='color: gray;'>👻 Badge should be hidden (no unread notifications)</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error querying database: " . $e->getMessage() . "</p>";
}

// Test the function
try {
    $functionCount = getUnreadNotificationCount($staffId);
    echo "<h2>Function Test:</h2>";
    echo "<p><strong>getUnreadNotificationCount() result:</strong> " . $functionCount . "</p>";
    
    if ($functionCount === $directCount['count']) {
        echo "<p style='color: green;'>✅ Function returns correct count</p>";
    } else {
        echo "<p style='color: red;'>❌ Function count mismatch!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing function: " . $e->getMessage() . "</p>";
}

// Show recent notifications
try {
    $recentNotifications = $database->fetchAll("
        SELECT id, title, message, category, is_read, created_at 
        FROM notifications 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ", [$staffId]);
    
    echo "<h2>Recent Notifications:</h2>";
    if (empty($recentNotifications)) {
        echo "<p>No notifications found</p>";
        echo "<p><a href='create_test_notification.php'>Create a test notification</a></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Title</th><th>Message</th><th>Category</th><th>Read</th><th>Created</th></tr>";
        foreach ($recentNotifications as $notif) {
            $readStatus = $notif['is_read'] ? '✅ Read' : '🔔 Unread';
            $rowStyle = $notif['is_read'] ? 'background: #f0f0f0;' : 'background: #ffe6e6;';
            echo "<tr style='{$rowStyle}'>";
            echo "<td>" . htmlspecialchars($notif['title']) . "</td>";
            echo "<td>" . htmlspecialchars($notif['message']) . "</td>";
            echo "<td>" . htmlspecialchars($notif['category']) . "</td>";
            echo "<td>{$readStatus}</td>";
            echo "<td>" . htmlspecialchars($notif['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting notifications: " . $e->getMessage() . "</p>";
}

// Test API endpoint
echo "<h2>API Test:</h2>";
echo "<button onclick='testAPI()'>Test API Endpoint</button>";
echo "<div id='apiResult'></div>";

?>

<script>
function testAPI() {
    const resultDiv = document.getElementById('apiResult');
    resultDiv.innerHTML = '<p>Testing API...</p>';
    
    fetch('<?= getBasePath() ?>/api/staff/notification-count.php')
        .then(response => {
            console.log('API Status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('API Response:', data);
            
            let html = '<h3>API Response:</h3>';
            html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            
            if (data.success) {
                html += '<p style="color: green;">✅ API working correctly</p>';
                html += '<p><strong>Count returned:</strong> ' + data.count + '</p>';
            } else {
                html += '<p style="color: red;">❌ API error: ' + data.error + '</p>';
            }
            
            resultDiv.innerHTML = html;
        })
        .catch(error => {
            console.error('API Error:', error);
            resultDiv.innerHTML = '<p style="color: red;">❌ API request failed: ' + error.message + '</p>';
        });
}

// Auto-test on page load
document.addEventListener('DOMContentLoaded', testAPI);
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
button:hover { background: #005a87; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style>

<p><a href="<?= getBasePath() ?>/staff/">← Back to Staff Dashboard</a></p>
