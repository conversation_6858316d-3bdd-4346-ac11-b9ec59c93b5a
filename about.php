@<?php
/**
 * About Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "About Us";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced About Page Styles */
.luxury-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.luxury-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.luxury-card:hover::before {
    left: 100%;
}

.luxury-card:hover {
    transform: translateY(-8px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(212, 175, 55, 0.1);
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-elements::before,
.floating-elements::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
}

.floating-elements::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-elements::after {
    bottom: 20%;
    right: 10%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-30px) rotate(180deg); opacity: 0.6; }
}

.parallax-bg {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.stats-counter {
    font-variant-numeric: tabular-nums;
    transition: all 0.3s ease;
}

.team-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(0, 0, 0, 0.98) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.team-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.05) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-card:hover::after {
    opacity: 1;
}

.team-card:hover {
    transform: translateY(-12px) scale(1.02);
    border-color: rgba(212, 175, 55, 0.4);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5), 0 0 40px rgba(212, 175, 55, 0.15);
}

.award-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.award-item:hover {
    transform: translateY(-5px) scale(1.05);
}

.award-item:hover .award-icon {
    transform: scale(1.2) rotate(10deg);
}

.award-icon {
    transition: all 0.3s ease;
}

.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    margin: 4rem 0;
}

.text-gradient {
    background: linear-gradient(135deg, #D4AF37, #F59E0B, #D4AF37);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

.stagger-animation {
    animation-delay: var(--delay, 0s);
}
</style>

    <!-- Enhanced Hero Section -->
    <section class="relative py-32 bg-gradient-to-br from-salon-black via-salon-black to-salon-black overflow-hidden">
        <!-- Background Elements -->
        <div class="floating-elements"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/3"></div>
        <div class="absolute top-20 left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-60 h-60 bg-salon-gold/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>

        <!-- Parallax Background Image -->
        <div class="absolute inset-0 opacity-10">
            <div class="parallax-bg h-full" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-6 text-center">
            <!-- Luxury Badge -->
            <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20 animate-on-scroll">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                Our Story
            </div>

            <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 leading-tight animate-on-scroll" style="--delay: 0.2s;">
                About
                <span class="text-gradient block md:inline">Flix Salon & SPA</span>
            </h1>

            <p class="text-xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed mb-12 animate-on-scroll" style="--delay: 0.4s;">
                Where luxury meets expertise, and every visit is a journey to discover your most beautiful self.
                <span class="block mt-4 text-salon-gold text-lg md:text-xl">Crafting beauty experiences since 2015</span>
            </p>

            <!-- Quick Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto animate-on-scroll" style="--delay: 0.6s;">
                <div class="text-center">
                    <div class="stats-counter text-4xl md:text-5xl font-bold text-salon-gold mb-2" data-target="5000">0</div>
                    <div class="text-gray-400 text-sm md:text-base">Happy Clients</div>
                </div>
                <div class="text-center">
                    <div class="stats-counter text-4xl md:text-5xl font-bold text-salon-gold mb-2" data-target="9">0</div>
                    <div class="text-gray-400 text-sm md:text-base">Years Experience</div>
                </div>
                <div class="text-center">
                    <div class="stats-counter text-4xl md:text-5xl font-bold text-salon-gold mb-2" data-target="15">0</div>
                    <div class="text-gray-400 text-sm md:text-base">Expert Staff</div>
                </div>
                <div class="text-center">
                    <div class="stats-counter text-4xl md:text-5xl font-bold text-salon-gold mb-2" data-target="50">0</div>
                    <div class="text-gray-400 text-sm md:text-base">Services Offered</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Our Story Section -->
    <section class="py-32 bg-secondary-900 relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 60px 60px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-6 relative">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
                <div class="animate-on-scroll">
                    <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Our Journey
                    </div>

                    <h2 class="text-5xl md:text-6xl font-bold font-serif text-white mb-8 leading-tight">
                        A Legacy of <span class="text-gradient">Beauty Excellence</span>
                    </h2>

                    <div class="space-y-6 mb-10">
                        <p class="text-xl text-gray-300 leading-relaxed">
                            Founded in 2015, Flix Salon & SPA began as a dream to create a sanctuary where beauty, luxury, and personalized care converge. What started as a small boutique salon has grown into the city's premier destination for comprehensive beauty services.
                        </p>
                        <p class="text-xl text-gray-300 leading-relaxed">
                            Our commitment to excellence, continuous education, and staying ahead of beauty trends has earned us the trust of over 5,000 satisfied clients. We believe that true beauty comes from confidence, and our mission is to help every client discover and embrace their unique radiance.
                        </p>
                    </div>

                    <!-- Enhanced Stats -->
                    <div class="grid grid-cols-2 gap-8">
                        <div class="luxury-card rounded-xl p-6 text-center">
                            <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div class="text-4xl font-bold text-salon-gold mb-2">5K+</div>
                            <div class="text-gray-400 font-medium">Happy Clients</div>
                        </div>
                        <div class="luxury-card rounded-xl p-6 text-center">
                            <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                </svg>
                            </div>
                            <div class="text-4xl font-bold text-salon-gold mb-2">9</div>
                            <div class="text-gray-400 font-medium">Years Experience</div>
                        </div>
                    </div>
                </div>

                <div class="relative animate-on-scroll" style="--delay: 0.3s;">
                    <!-- Enhanced Visual Element -->
                    <div class="relative">
                        <div class="aspect-square bg-gradient-to-br from-salon-gold/20 via-secondary-900 to-salon-gold/10 rounded-3xl flex items-center justify-center relative overflow-hidden">
                            <!-- Background Pattern -->
                            <div class="absolute inset-0 opacity-20">
                                <div class="absolute inset-0" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'); background-size: cover; background-position: center;"></div>
                            </div>

                            <!-- Content -->
                            <div class="text-center relative z-10">
                                <div class="w-24 h-24 bg-salon-gold/30 rounded-full flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
                                    <svg class="w-12 h-12 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                </div>
                                <div class="text-white text-2xl font-bold mb-2">Luxury Beauty Experience</div>
                                <div class="text-salon-gold text-lg">Crafted with Passion</div>
                            </div>

                            <!-- Decorative Elements -->
                            <div class="absolute top-4 right-4 w-8 h-8 bg-salon-gold/20 rounded-full"></div>
                            <div class="absolute bottom-4 left-4 w-6 h-6 bg-salon-gold/30 rounded-full"></div>
                            <div class="absolute top-1/2 left-4 w-4 h-4 bg-salon-gold/25 rounded-full"></div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="absolute -top-4 -right-4 w-16 h-16 bg-salon-gold/10 rounded-full blur-xl"></div>
                        <div class="absolute -bottom-4 -left-4 w-20 h-20 bg-salon-gold/5 rounded-full blur-2xl"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Mission & Values -->
    <section class="py-32 bg-salon-black relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute top-0 left-0 w-96 h-96 bg-salon-gold/5 rounded-full blur-3xl"></div>
        <div class="absolute bottom-0 right-0 w-80 h-80 bg-salon-gold/3 rounded-full blur-3xl"></div>

        <div class="max-w-7xl mx-auto px-6 relative">
            <div class="text-center mb-20 animate-on-scroll">
                <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    Our Values
                </div>
                <h2 class="text-5xl md:text-6xl font-bold font-serif text-white mb-8 leading-tight">
                    What We <span class="text-gradient">Stand For</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                    Our core values guide every interaction, service, and decision we make at Flix Salon & SPA
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <div class="luxury-card rounded-2xl p-8 text-center animate-on-scroll" style="--delay: 0.1s;">
                    <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                        <svg class="w-12 h-12 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-salon-gold/30 rounded-full"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-6">Personalized Care</h3>
                    <p class="text-gray-300 leading-relaxed text-lg">
                        Every client is unique, and we tailor our services to meet your individual needs, preferences, and beauty goals. Your satisfaction is our masterpiece.
                    </p>
                </div>

                <div class="luxury-card rounded-2xl p-8 text-center animate-on-scroll" style="--delay: 0.2s;">
                    <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                        <svg class="w-12 h-12 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-salon-gold/30 rounded-full"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-6">Excellence</h3>
                    <p class="text-gray-300 leading-relaxed text-lg">
                        We maintain the highest standards in everything we do, from our services to our products and customer experience. Excellence is not just our goal—it's our promise.
                    </p>
                </div>

                <div class="luxury-card rounded-2xl p-8 text-center animate-on-scroll" style="--delay: 0.3s;">
                    <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                        <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-salon-gold/30 rounded-full"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-6">Sustainability</h3>
                    <p class="text-gray-300 leading-relaxed text-lg">
                        We're committed to eco-friendly practices and use sustainable, cruelty-free products. Beauty should enhance the world, not harm it.
                    </p>
                </div>
            </div>

            <!-- Additional Values Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-10 mt-16">
                <div class="luxury-card rounded-2xl p-8 text-center animate-on-scroll" style="--delay: 0.4s;">
                    <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                        <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-salon-gold/30 rounded-full"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-6">Continuous Learning</h3>
                    <p class="text-gray-300 leading-relaxed text-lg">
                        We stay ahead of beauty trends through continuous education and training, ensuring you always receive the latest techniques and innovations.
                    </p>
                </div>

                <div class="luxury-card rounded-2xl p-8 text-center animate-on-scroll" style="--delay: 0.5s;">
                    <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                        <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-salon-gold/30 rounded-full"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-6">Community Focus</h3>
                    <p class="text-gray-300 leading-relaxed text-lg">
                        We believe in giving back to our community and building lasting relationships that extend beyond the salon chair.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Team Section -->
    <section class="py-32 bg-salon-black relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, #D4AF37 1px, transparent 1px), radial-gradient(circle at 70% 70%, #D4AF37 1px, transparent 1px); background-size: 40px 40px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-6 relative">
            <div class="text-center mb-20 animate-on-scroll">
                <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Meet Our Team
                </div>
                <h2 class="text-5xl md:text-6xl font-bold font-serif text-white mb-8 leading-tight">
                    Expert <span class="text-gradient">Professionals</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    Our talented team of certified professionals brings years of experience and passion for beauty to every service.
                    <span class="text-salon-gold">Meet the artists behind your transformation.</span>
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php
                // Include staff functions if not already included
                require_once __DIR__ . '/includes/staff_functions.php';
                
                // Get all active staff members
                $staffMembers = getActiveStaff();
                
                // Get staff schedules to extract roles
                $staffRoles = [];
                foreach ($staffMembers as $staff) {
                    $staffSchedule = $database->fetch(
                        "SELECT role FROM staff_schedules WHERE user_id = ?",
                        [$staff['id']]
                    );
                    $staffRoles[$staff['id']] = $staffSchedule ? $staffSchedule['role'] : 'Staff Member';
                }
                
                // Get staff specialties
                $staffSpecialties = [];
                foreach ($staffMembers as $staff) {
                    $specialties = $database->fetchAll(
                        "SELECT s.name FROM staff_specialties ss 
                         JOIN services s ON ss.service_id = s.id 
                         WHERE ss.user_id = ? 
                         LIMIT 3",
                        [$staff['id']]
                    );
                    
                    $staffSpecialties[$staff['id']] = array_map(function($specialty) {
                        return $specialty['name'];
                    }, $specialties);
                }
                
                foreach ($staffMembers as $index => $staff): ?>
                    <div class="animate-on-scroll" style="--delay: <?= ($index * 0.1) ?>s;">
                        <div class="team-card rounded-3xl p-8 relative">
                            <!-- Profile Image Placeholder -->
                            <div class="relative mb-8">
                                <div class="w-32 h-32 bg-gradient-to-br from-salon-gold/30 to-salon-gold/10 rounded-full flex items-center justify-center mx-auto relative overflow-hidden">
                                    <!-- Background Pattern -->
                                    <div class="absolute inset-0 opacity-20">
                                        <div class="w-full h-full" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'); background-size: cover; background-position: center;"></div>
                                    </div>

                                    <!-- Initials -->
                                    <span class="text-salon-gold font-bold text-2xl relative z-10">
                                        <?php 
                                        $nameParts = explode(' ', $staff['name']);
                                        echo strtoupper(substr($nameParts[0], 0, 1)) . (isset($nameParts[1]) ? strtoupper(substr($nameParts[1], 0, 1)) : '');
                                        ?>
                                    </span>

                                    <!-- Decorative Ring -->
                                    <div class="absolute inset-0 border-2 border-salon-gold/30 rounded-full"></div>
                                </div>

                                <!-- Floating Badge -->
                                <div class="absolute -top-2 -right-2 w-8 h-8 bg-salon-gold rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="text-center">
                                <h3 class="text-2xl font-bold text-white mb-3"><?= $staff['name'] ?></h3>
                                <p class="text-salon-gold font-semibold text-lg mb-3"><?= $staffRoles[$staff['id']] ?></p>

                                <!-- Experience Badge -->
                                <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-medium mb-6 border border-salon-gold/20">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Professional Expert
                                </div>

                                <!-- Specialties -->
                                <div class="flex flex-wrap justify-center gap-2 mb-6">
                                    <?php 
                                    $specialties = $staffSpecialties[$staff['id']] ?? [];
                                    if (empty($specialties)) {
                                        $specialties = ['Beauty Expert'];
                                    }
                                    foreach ($specialties as $specialty): 
                                    ?>
                                        <span class="bg-salon-gold/15 text-salon-gold px-3 py-1 rounded-full text-sm font-medium border border-salon-gold/20">
                                            <?= $specialty ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Bio -->
                                <p class="text-gray-300 leading-relaxed text-base">
                                    <?= $staff['bio'] ?? 'A dedicated beauty professional committed to helping clients look and feel their best. Specializes in creating personalized beauty experiences.' ?>
                                </p>

                                <!-- Contact Button -->
                                <div class="mt-6">
                                    <a href="<?= getBasePath() ?>/customer/book" class="inline-flex items-center text-salon-gold hover:text-white transition-colors font-medium">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        Book with <?= explode(' ', $staff['name'])[0] ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Awards & Recognition -->
    <section class="py-24 bg-salon-black">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                    Recognition
                </div>
                <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                    Awards & <span class="text-salon-gold">Achievements</span>
                </h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-trophy text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Best Salon 2023</h3>
                    <p class="text-gray-400 text-sm">City Beauty Awards</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-star text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">5-Star Rating</h3>
                    <p class="text-gray-400 text-sm">Google Reviews</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-certificate text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Certified Professionals</h3>
                    <p class="text-gray-400 text-sm">Licensed & Insured</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-handshake text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Premium Partners</h3>
                    <p class="text-gray-400 text-sm">Top Beauty Brands</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-secondary-900">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <div class="bg-gradient-to-r from-salon-gold/10 to-transparent border border-salon-gold/20 rounded-3xl p-12">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-6">
                    Experience the <span class="text-salon-gold">Flix Salon & SPA</span> Difference
                </h2>
                <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                    Join thousands of satisfied clients who trust us with their beauty needs. Book your appointment today and discover why we're the city's premier salon.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Book Appointment
                    </a>
                    <a href="<?= getBasePath() ?>/contact.php" class="bg-secondary-800 hover:bg-secondary-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-secondary-600">
                        <i class="fas fa-phone mr-2"></i>
                        Contact Us
                    </a>
                </div>
            </div>
        </div>
    </section>

<script>
// Enhanced About Page Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Animate stats counters
    function animateCounters() {
        const counters = document.querySelectorAll('.stats-counter');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const increment = target / 100;
            let current = 0;

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    if (current > target) current = target;

                    if (target >= 1000) {
                        counter.textContent = Math.floor(current).toLocaleString() + '+';
                    } else {
                        counter.textContent = Math.floor(current);
                    }

                    requestAnimationFrame(updateCounter);
                } else {
                    if (target >= 1000) {
                        counter.textContent = target.toLocaleString() + '+';
                    } else {
                        counter.textContent = target;
                    }
                }
            };

            updateCounter();
        });
    }

    // Scroll animations
    function handleScrollAnimations() {
        const elements = document.querySelectorAll('.animate-on-scroll');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate');
            }
        });
    }

    // Intersection Observer for better performance
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');

                // Trigger counter animation when stats section is visible
                if (entry.target.querySelector('.stats-counter')) {
                    setTimeout(animateCounters, 300);
                }
            }
        });
    }, observerOptions);

    // Observe all animated elements
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Parallax effect for hero background
    function handleParallax() {
        const parallaxElements = document.querySelectorAll('.parallax-bg');
        const scrolled = window.pageYOffset;

        parallaxElements.forEach(element => {
            const rate = scrolled * -0.5;
            element.style.transform = `translateY(${rate}px)`;
        });
    }

    // Smooth scroll for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Enhanced hover effects for team cards
    document.querySelectorAll('.team-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add scroll event listeners
    let ticking = false;

    function updateOnScroll() {
        handleScrollAnimations();
        handleParallax();
        ticking = false;
    }

    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateOnScroll);
            ticking = true;
        }
    });

    // Initial check for elements already in view
    handleScrollAnimations();

    // Add loading animation to page
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Add floating animation to decorative elements
function addFloatingAnimation() {
    const floatingElements = document.querySelectorAll('.floating-elements');

    floatingElements.forEach(element => {
        let position = 0;

        function animate() {
            position += 0.5;
            element.style.transform = `translateY(${Math.sin(position * 0.01) * 10}px)`;
            requestAnimationFrame(animate);
        }

        animate();
    });
}

// Initialize floating animations
setTimeout(addFloatingAnimation, 1000);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
