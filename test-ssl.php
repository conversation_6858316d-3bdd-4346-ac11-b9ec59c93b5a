<?php
/**
 * Test SSL configuration for cURL
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/payment_functions.php';

echo "<h1>SSL Configuration Test</h1>";

// Test 1: Basic cURL with SSL disabled
echo "<h2>Test 1: Basic cURL Test</h2>";
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "https://httpbin.org/get",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_TIMEOUT => 10
]);

$response = curl_exec($curl);
$error = curl_error($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

if ($error) {
    echo "<p style='color: red;'>❌ cURL Error: " . htmlspecialchars($error) . "</p>";
} else {
    echo "<p style='color: green;'>✅ cURL Success - HTTP Code: " . $httpCode . "</p>";
}

// Test 2: Test Flutterwave API endpoint (if enabled)
if (FLUTTERWAVE_ENABLED && FLUTTERWAVE_SECRET_KEY) {
    echo "<h2>Test 2: Flutterwave API Test</h2>";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => "https://api.flutterwave.com/v3/transactions/verify_by_reference?tx_ref=test",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            "Authorization: Bearer " . FLUTTERWAVE_SECRET_KEY,
            "Content-Type: application/json"
        ]
    ]);
    
    $response = curl_exec($curl);
    $error = curl_error($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($error) {
        echo "<p style='color: red;'>❌ Flutterwave API Error: " . htmlspecialchars($error) . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Flutterwave API Success - HTTP Code: " . $httpCode . "</p>";
        if ($httpCode == 400) {
            echo "<p style='color: orange;'>ℹ️ HTTP 400 is expected for test transaction reference</p>";
        }
    }
} else {
    echo "<h2>Test 2: Flutterwave API Test</h2>";
    echo "<p style='color: orange;'>⚠️ Flutterwave not enabled or secret key not set</p>";
}

// Test 3: Test using our configureCurlSSL function
echo "<h2>Test 3: Using configureCurlSSL Function</h2>";
$curl = curl_init();
configureCurlSSL($curl, [
    CURLOPT_URL => "https://httpbin.org/get"
]);

$response = curl_exec($curl);
$error = curl_error($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

if ($error) {
    echo "<p style='color: red;'>❌ configureCurlSSL Error: " . htmlspecialchars($error) . "</p>";
} else {
    echo "<p style='color: green;'>✅ configureCurlSSL Success - HTTP Code: " . $httpCode . "</p>";
}

// Test 4: Show current PHP and cURL configuration
echo "<h2>Test 4: System Information</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>cURL Version:</strong> " . curl_version()['version'] . "</p>";
echo "<p><strong>SSL Version:</strong> " . curl_version()['ssl_version'] . "</p>";
echo "<p><strong>OpenSSL Support:</strong> " . (extension_loaded('openssl') ? 'Yes' : 'No') . "</p>";

// Test 5: Environment-specific recommendations
echo "<h2>Test 5: Recommendations</h2>";
if (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || 
    strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
    strpos($_SERVER['HTTP_HOST'], '.local') !== false) {
    echo "<p style='color: blue;'>ℹ️ Development environment detected. SSL verification disabled is acceptable.</p>";
} else {
    echo "<p style='color: red;'>⚠️ Production environment detected. Consider enabling SSL verification for security.</p>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If all tests pass, the SSL configuration should work for payments</li>";
echo "<li>Try the payment flow again</li>";
echo "<li>If issues persist, check XAMPP SSL configuration</li>";
echo "</ul>";
?>
