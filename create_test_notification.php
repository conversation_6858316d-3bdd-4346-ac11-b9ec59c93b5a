<?php
/**
 * Create test notifications for debugging
 */

session_start();
require_once __DIR__ . '/config/app.php';

// Check if user is logged in as staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    echo "<h1>Error</h1>";
    echo "<p>Please log in as a staff member first.</p>";
    echo "<a href='/flix-php/auth/login.php'>Login</a>";
    exit;
}

$staffId = $_SESSION['user_id'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create') {
        // Create a test notification
        $notificationId = generateUUID();
        $title = $_POST['title'] ?? 'Test Notification';
        $message = $_POST['message'] ?? 'This is a test notification created at ' . date('Y-m-d H:i:s');
        $category = $_POST['category'] ?? 'SYSTEM';
        $priority = $_POST['priority'] ?? 'MEDIUM';
        
        try {
            $database->query(
                "INSERT INTO notifications (id, user_id, title, message, category, priority, is_read, created_at, updated_at) 
                 VALUES (?, ?, ?, ?, ?, ?, 0, NOW(), NOW())",
                [$notificationId, $staffId, $title, $message, $category, $priority]
            );
            
            $success = "Test notification created successfully! ID: " . $notificationId;
        } catch (Exception $e) {
            $error = "Error creating notification: " . $e->getMessage();
        }
    } elseif ($action === 'clear') {
        // Clear all notifications for this staff member
        try {
            $database->query("DELETE FROM notifications WHERE user_id = ?", [$staffId]);
            $success = "All notifications cleared successfully!";
        } catch (Exception $e) {
            $error = "Error clearing notifications: " . $e->getMessage();
        }
    }
}

// Get current notification count
try {
    $counts = $database->fetch("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
            SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as read
        FROM notifications
        WHERE user_id = ?
    ", [$staffId]);
} catch (Exception $e) {
    $counts = ['total' => 0, 'unread' => 0, 'read' => 0];
    $error = "Error getting counts: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notification Creator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        button:hover { background: #005a87; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-bottom: 15px; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 15px; }
        .stats { background: #e9ecef; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .counter-test { background: #fff3cd; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Notification Creator</h1>
        <p><strong>Staff:</strong> <?= htmlspecialchars($_SESSION['user_name']) ?> (<?= htmlspecialchars($staffId) ?>)</p>
        
        <?php if (isset($success)): ?>
            <div class="success"><?= htmlspecialchars($success) ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>
        
        <div class="stats">
            <h3>Current Notification Stats</h3>
            <p><strong>Total:</strong> <?= $counts['total'] ?></p>
            <p><strong>Unread:</strong> <?= $counts['unread'] ?></p>
            <p><strong>Read:</strong> <?= $counts['read'] ?></p>
        </div>
        
        <div class="counter-test">
            <h3>Counter Test</h3>
            <p>Test counter: <span id="testCounter" style="background: red; color: white; padding: 2px 6px; border-radius: 50%; font-size: 12px;">0</span></p>
            <button onclick="testCounter()">Test Counter Update</button>
        </div>
        
        <form method="POST">
            <input type="hidden" name="action" value="create">
            
            <div class="form-group">
                <label for="title">Notification Title:</label>
                <input type="text" name="title" id="title" value="Test Notification" required>
            </div>
            
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea name="message" id="message" rows="3" required>This is a test notification to verify the counter functionality.</textarea>
            </div>
            
            <div class="form-group">
                <label for="category">Category:</label>
                <select name="category" id="category">
                    <option value="SYSTEM">System</option>
                    <option value="BOOKING">Booking</option>
                    <option value="STAFF">Staff</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="priority">Priority:</label>
                <select name="priority" id="priority">
                    <option value="LOW">Low</option>
                    <option value="MEDIUM" selected>Medium</option>
                    <option value="HIGH">High</option>
                </select>
            </div>
            
            <button type="submit">Create Test Notification</button>
        </form>
        
        <form method="POST" style="margin-top: 20px;">
            <input type="hidden" name="action" value="clear">
            <button type="submit" class="danger" onclick="return confirm('Are you sure you want to clear all notifications?')">Clear All Notifications</button>
        </form>
        
        <div style="margin-top: 20px;">
            <a href="<?= getBasePath() ?>/staff/">← Back to Staff Dashboard</a> |
            <a href="<?= getBasePath() ?>/staff/notifications/">View Notifications</a>
        </div>
    </div>

    <script>
        function testCounter() {
            const counter = document.getElementById('testCounter');
            
            fetch('<?= getBasePath() ?>/api/staff/notifications.php')
                .then(response => response.json())
                .then(data => {
                    console.log('API Response:', data);
                    
                    if (data.error) {
                        counter.textContent = 'ERR';
                        counter.style.background = 'orange';
                        return;
                    }
                    
                    const unreadCount = data.data.counts.unread;
                    counter.textContent = unreadCount > 99 ? '99+' : unreadCount;
                    counter.style.background = unreadCount > 0 ? 'red' : 'gray';
                    
                    // Also update the page stats
                    setTimeout(() => location.reload(), 1000);
                })
                .catch(error => {
                    console.error('Error:', error);
                    counter.textContent = 'ERR';
                    counter.style.background = 'red';
                });
        }
        
        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', testCounter);
    </script>
</body>
</html>
