<?php
/**
 * Staff Schedules Cleanup Script
 * Removes duplicate staff schedule entries and ensures each staff member has only one record
 */

require_once __DIR__ . '/config/app.php';

// Check if user is admin (for security)
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Staff Schedules Cleanup Tool</h1>";
echo "<p>This tool will remove duplicate staff schedule entries and ensure each staff member has only one record.</p>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_cleanup'])) {
    try {
        $database->beginTransaction();
        
        echo "<h2>Starting Cleanup Process...</h2>";
        
        // Step 1: Check current duplicates
        echo "<h3>Step 1: Checking for duplicates</h3>";
        $duplicates = $database->fetchAll("
            SELECT 
                user_id,
                COUNT(*) as duplicate_count,
                GROUP_CONCAT(id) as schedule_ids
            FROM staff_schedules
            WHERE user_id IS NOT NULL
            GROUP BY user_id
            HAVING COUNT(*) > 1
        ");
        
        if (empty($duplicates)) {
            echo "<p style='color: green;'>✅ No duplicates found. All staff members have unique schedule records.</p>";
            $database->rollback();
            exit;
        }
        
        echo "<p>Found duplicates for " . count($duplicates) . " staff members:</p>";
        echo "<ul>";
        foreach ($duplicates as $dup) {
            $user = $database->fetch("SELECT name FROM users WHERE id = ?", [$dup['user_id']]);
            echo "<li>Staff: " . htmlspecialchars($user['name'] ?? 'Unknown') . " (ID: {$dup['user_id']}) - {$dup['duplicate_count']} records</li>";
        }
        echo "</ul>";
        
        // Step 2: Create backup
        echo "<h3>Step 2: Creating backup</h3>";
        $database->query("CREATE TABLE IF NOT EXISTS staff_schedules_backup_" . date('Y_m_d_H_i_s') . " AS SELECT * FROM staff_schedules");
        echo "<p style='color: green;'>✅ Backup created successfully.</p>";
        
        // Step 3: Identify records to keep (most recent for each user)
        echo "<h3>Step 3: Identifying records to keep</h3>";
        $keepRecords = $database->fetchAll("
            SELECT ss1.id
            FROM staff_schedules ss1
            INNER JOIN (
                SELECT 
                    user_id,
                    MAX(updated_at) as latest_update
                FROM staff_schedules
                WHERE user_id IS NOT NULL
                GROUP BY user_id
            ) ss2 ON ss1.user_id = ss2.user_id AND ss1.updated_at = ss2.latest_update
        ");
        
        $keepIds = array_column($keepRecords, 'id');
        echo "<p>Will keep " . count($keepIds) . " records (most recent for each staff member).</p>";
        
        // Step 4: Remove duplicates
        echo "<h3>Step 4: Removing duplicate records</h3>";
        $placeholders = str_repeat('?,', count($keepIds) - 1) . '?';
        $deleteResult = $database->query("DELETE FROM staff_schedules WHERE id NOT IN ($placeholders)", $keepIds);
        echo "<p style='color: green;'>✅ Removed duplicate records.</p>";
        
        // Step 5: Ensure all active staff have a schedule record
        echo "<h3>Step 5: Ensuring all active staff have schedule records</h3>";
        $database->query("
            INSERT IGNORE INTO staff_schedules (id, user_id, role, hourly_rate, schedule, created_at, updated_at)
            SELECT 
                CONCAT('schedule-', u.id) as id,
                u.id as user_id,
                'Staff Member' as role,
                500000 as hourly_rate,
                JSON_OBJECT(
                    'monday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
                    'tuesday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
                    'wednesday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
                    'thursday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
                    'friday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
                    'saturday', JSON_OBJECT('start_time', '10:00', 'end_time', '16:00', 'is_working', true),
                    'sunday', JSON_OBJECT('start_time', '10:00', 'end_time', '16:00', 'is_working', false)
                ) as schedule,
                NOW() as created_at,
                NOW() as updated_at
            FROM users u
            WHERE u.role = 'STAFF' 
            AND u.is_active = 1
            AND NOT EXISTS (
                SELECT 1 FROM staff_schedules ss WHERE ss.user_id = u.id
            )
        ");
        echo "<p style='color: green;'>✅ Ensured all active staff have schedule records.</p>";
        
        // Step 6: Add unique constraint to prevent future duplicates
        echo "<h3>Step 6: Adding unique constraint</h3>";
        try {
            $database->query("ALTER TABLE staff_schedules ADD CONSTRAINT unique_user_schedule UNIQUE KEY (user_id)");
            echo "<p style='color: green;'>✅ Added unique constraint to prevent future duplicates.</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: orange;'>⚠️ Unique constraint already exists.</p>";
            } else {
                throw $e;
            }
        }
        
        // Step 7: Final verification
        echo "<h3>Step 7: Final verification</h3>";
        $finalCheck = $database->fetchAll("
            SELECT 
                user_id,
                COUNT(*) as record_count
            FROM staff_schedules
            WHERE user_id IS NOT NULL
            GROUP BY user_id
            HAVING COUNT(*) > 1
        ");
        
        if (empty($finalCheck)) {
            echo "<p style='color: green;'>✅ Success! All staff members now have exactly one schedule record.</p>";
        } else {
            echo "<p style='color: red;'>❌ Warning: Some duplicates still exist:</p>";
            foreach ($finalCheck as $check) {
                echo "<p>User ID: {$check['user_id']} has {$check['record_count']} records</p>";
            }
        }
        
        // Show final statistics
        $totalStaff = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF' AND is_active = 1")['count'];
        $totalSchedules = $database->fetch("SELECT COUNT(*) as count FROM staff_schedules WHERE user_id IS NOT NULL")['count'];
        
        echo "<h3>Final Statistics</h3>";
        echo "<p>Active Staff Members: {$totalStaff}</p>";
        echo "<p>Staff Schedule Records: {$totalSchedules}</p>";
        
        $database->commit();
        echo "<h2 style='color: green;'>✅ Cleanup completed successfully!</h2>";
        echo "<p><a href='/flix-php/admin/staff/'>Go to Staff Management</a></p>";
        
    } catch (Exception $e) {
        $database->rollback();
        echo "<h2 style='color: red;'>❌ Error during cleanup:</h2>";
        echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>All changes have been rolled back.</p>";
    }
} else {
    // Show confirmation form
    echo "<h2>Pre-Cleanup Analysis</h2>";
    
    // Check current state
    $duplicates = $database->fetchAll("
        SELECT 
            user_id,
            COUNT(*) as duplicate_count
        FROM staff_schedules
        WHERE user_id IS NOT NULL
        GROUP BY user_id
        HAVING COUNT(*) > 1
    ");
    
    $totalStaff = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF' AND is_active = 1")['count'];
    $totalSchedules = $database->fetch("SELECT COUNT(*) as count FROM staff_schedules WHERE user_id IS NOT NULL")['count'];
    
    echo "<p><strong>Current Status:</strong></p>";
    echo "<ul>";
    echo "<li>Active Staff Members: {$totalStaff}</li>";
    echo "<li>Staff Schedule Records: {$totalSchedules}</li>";
    echo "<li>Staff with Duplicate Schedules: " . count($duplicates) . "</li>";
    echo "</ul>";
    
    if (empty($duplicates)) {
        echo "<p style='color: green;'>✅ No cleanup needed. All staff members have unique schedule records.</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Cleanup needed. The following staff members have duplicate schedule records:</p>";
        echo "<ul>";
        foreach ($duplicates as $dup) {
            $user = $database->fetch("SELECT name FROM users WHERE id = ?", [$dup['user_id']]);
            echo "<li>" . htmlspecialchars($user['name'] ?? 'Unknown') . " - {$dup['duplicate_count']} records</li>";
        }
        echo "</ul>";
        
        echo "<h3>Cleanup Actions</h3>";
        echo "<p>The cleanup process will:</p>";
        echo "<ol>";
        echo "<li>Create a backup of the current staff_schedules table</li>";
        echo "<li>Keep the most recent schedule record for each staff member</li>";
        echo "<li>Remove all duplicate records</li>";
        echo "<li>Ensure all active staff have a schedule record</li>";
        echo "<li>Add a unique constraint to prevent future duplicates</li>";
        echo "</ol>";
        
        echo "<form method='POST' style='margin-top: 20px;'>";
        echo "<p><strong>⚠️ Warning:</strong> This action will permanently remove duplicate records. A backup will be created first.</p>";
        echo "<label><input type='checkbox' name='confirm_cleanup' value='1' required> I understand and want to proceed with the cleanup</label><br><br>";
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Start Cleanup Process</button>";
        echo "</form>";
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
ul, ol { margin-left: 20px; }
p { margin: 10px 0; }
</style>
