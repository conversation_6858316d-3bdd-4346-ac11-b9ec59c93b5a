<?php
/**
 * Test Flutterwave API connection and configuration
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Flutterwave API Test</h1>";

// Test 1: Check configuration
echo "<h2>Test 1: Configuration Check</h2>";
echo "<p><strong>Flutterwave Enabled:</strong> " . (FLUTTERWAVE_ENABLED ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Public Key:</strong> " . (FLUTTERWAVE_PUBLIC_KEY ? substr(FLUTTERWAVE_PUBLIC_KEY, 0, 20) . '...' : 'Not set') . "</p>";
echo "<p><strong>Secret Key:</strong> " . (FLUTTERWAVE_SECRET_KEY ? substr(FLUTTERWAVE_SECRET_KEY, 0, 20) . '...' : 'Not set') . "</p>";
echo "<p><strong>Currency:</strong> " . CURRENCY_CODE . "</p>";

if (!FLUTTERWAVE_ENABLED) {
    echo "<p style='color: red;'>❌ Flutterwave is not enabled</p>";
    exit;
}

if (!FLUTTERWAVE_SECRET_KEY) {
    echo "<p style='color: red;'>❌ Flutterwave secret key is not set</p>";
    exit;
}

// Test 2: Check if using test keys
echo "<h2>Test 2: Environment Check</h2>";
$isTestMode = strpos(FLUTTERWAVE_PUBLIC_KEY, 'FLWPUBK_TEST') === 0;
echo "<p><strong>Test Mode:</strong> " . ($isTestMode ? 'Yes' : 'No') . "</p>";

if ($isTestMode) {
    echo "<p style='color: orange;'>⚠️ Using test keys - transactions will be simulated</p>";
} else {
    echo "<p style='color: green;'>✅ Using live keys - real transactions</p>";
}

// Test 3: Test API connectivity
echo "<h2>Test 3: API Connectivity Test</h2>";

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "https://api.flutterwave.com/v3/transactions/verify_by_reference?tx_ref=test_ref_" . time(),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_HTTPHEADER => [
        "Authorization: Bearer " . FLUTTERWAVE_SECRET_KEY,
        "Content-Type: application/json"
    ]
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$error = curl_error($curl);
curl_close($curl);

if ($error) {
    echo "<p style='color: red;'>❌ cURL Error: " . htmlspecialchars($error) . "</p>";
} else {
    echo "<p style='color: green;'>✅ API Connection Success - HTTP Code: " . $httpCode . "</p>";
    
    if ($httpCode == 400) {
        echo "<p style='color: orange;'>ℹ️ HTTP 400 is expected for non-existent transaction reference</p>";
    }
    
    $result = json_decode($response, true);
    if ($result) {
        echo "<p><strong>API Response:</strong></p>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT));
        echo "</pre>";
    }
}

// Test 4: Test with a sample payment verification
echo "<h2>Test 4: Sample Payment Verification</h2>";

// Check if we have any payments in the database
global $database;
$samplePayment = $database->fetch("
    SELECT * FROM payments 
    WHERE payment_gateway = 'FLUTTERWAVE' 
    AND flutterwave_tx_ref IS NOT NULL 
    ORDER BY created_at DESC 
    LIMIT 1
");

if ($samplePayment) {
    echo "<p><strong>Testing with payment:</strong> " . htmlspecialchars($samplePayment['id']) . "</p>";
    echo "<p><strong>Transaction Reference:</strong> " . htmlspecialchars($samplePayment['flutterwave_tx_ref']) . "</p>";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => "https://api.flutterwave.com/v3/transactions/verify_by_reference?tx_ref=" . urlencode($samplePayment['flutterwave_tx_ref']),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            "Authorization: Bearer " . FLUTTERWAVE_SECRET_KEY,
            "Content-Type: application/json"
        ]
    ]);
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $error = curl_error($curl);
    curl_close($curl);
    
    if ($error) {
        echo "<p style='color: red;'>❌ Verification Error: " . htmlspecialchars($error) . "</p>";
    } else {
        echo "<p><strong>Verification Response - HTTP Code:</strong> " . $httpCode . "</p>";
        
        $result = json_decode($response, true);
        if ($result) {
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT));
            echo "</pre>";
            
            if ($httpCode == 404) {
                echo "<p style='color: orange;'>ℹ️ HTTP 404 means the transaction doesn't exist in Flutterwave's system</p>";
                echo "<p style='color: blue;'>💡 This is normal for test transactions that weren't completed through Flutterwave</p>";
            }
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ No Flutterwave payments found in database</p>";
}

// Test 5: Recommendations
echo "<h2>Test 5: Recommendations</h2>";
echo "<ul>";

if ($isTestMode) {
    echo "<li style='color: blue;'>ℹ️ You're in test mode - the verification system will simulate successful payments</li>";
    echo "<li style='color: green;'>✅ Try the payment flow - it should work with mock verification</li>";
} else {
    echo "<li style='color: orange;'>⚠️ You're in live mode - ensure you have real Flutterwave transactions</li>";
}

echo "<li>🔧 Check the error logs for detailed debugging information</li>";
echo "<li>📝 The verification system now handles both real and test transactions</li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Try the payment flow again</li>";
echo "<li>Check the browser console and server logs for detailed information</li>";
echo "<li>If using test mode, the payment should now complete successfully</li>";
echo "</ol>";
?>
