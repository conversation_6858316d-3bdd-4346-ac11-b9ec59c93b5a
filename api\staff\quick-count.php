<?php
/**
 * SUPER FAST notification count endpoint
 * Minimal overhead for instant response
 */

// Start output buffering and set headers immediately
ob_start();
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Access-Control-Allow-Origin: *');

// Start session quickly
session_start();

// Quick auth check
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'STAFF') {
    echo '{"success":false,"count":0,"error":"auth"}';
    exit;
}

$staffId = $_SESSION['user_id'];

// Direct database connection (bypass framework overhead)
try {
    require_once __DIR__ . '/../../config/database.php';
    
    // Use the existing database connection
    global $database;
    
    // Simple, fast query
    $stmt = $database->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$staffId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $count = (int)($result['count'] ?? 0);
    
    // Return minimal JSON
    echo json_encode([
        'success' => true,
        'count' => $count,
        'time' => microtime(true)
    ]);
    
} catch (Exception $e) {
    echo '{"success":false,"count":0,"error":"db"}';
}

ob_end_flush();
?>
