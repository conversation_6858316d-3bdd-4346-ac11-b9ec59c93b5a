<?php
/**
 * Staff Notifications API
 * Handles notification operations for staff members
 * Flix Salonce - PHP Version
 */

session_start();
require_once __DIR__ . '/../../config/app.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetNotifications();
            break;
        case 'PUT':
            handleUpdateNotification();
            break;
        case 'DELETE':
            handleDeleteNotification();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetNotifications() {
    global $database;

    try {
        // Check if notifications table exists
        $database->fetch("SELECT 1 FROM notifications LIMIT 1");
    } catch (Exception $e) {
        // Table doesn't exist
        http_response_code(500);
        echo json_encode([
            'error' => 'Notifications table does not exist. Please run the migration first.',
            'migration_url' => '/admin/notifications/migrate.php'
        ]);
        return;
    }

    $userId = $_SESSION['user_id'];

    // Check if requesting a single notification by ID
    if (isset($_GET['id'])) {
        handleGetSingleNotification($_GET['id'], $userId);
        return;
    }

    // Get query parameters
    $limit = min((int)($_GET['limit'] ?? 20), 100);
    $offset = max((int)($_GET['offset'] ?? 0), 0);
    $category = $_GET['category'] ?? null;
    $isRead = $_GET['is_read'] ?? null;
    $priority = $_GET['priority'] ?? null;
    
    // Build WHERE conditions
    $whereConditions = ['user_id = ?'];
    $params = [$userId];
    
    if ($category) {
        $whereConditions[] = 'category = ?';
        $params[] = $category;
    }
    
    if ($isRead !== null) {
        $whereConditions[] = 'is_read = ?';
        $params[] = $isRead === 'true' ? 1 : 0;
    }
    
    if ($priority) {
        $whereConditions[] = 'priority = ?';
        $params[] = $priority;
    }
    
    // Add expiration check
    $whereConditions[] = '(expires_at IS NULL OR expires_at > NOW())';
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Get notifications
    $notifications = $database->fetchAll(
        "SELECT * FROM notifications 
         WHERE {$whereClause}
         ORDER BY 
            CASE priority 
                WHEN 'URGENT' THEN 1 
                WHEN 'HIGH' THEN 2 
                WHEN 'MEDIUM' THEN 3 
                WHEN 'LOW' THEN 4 
            END,
            created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, [$limit, $offset])
    );
    
    // Get total count
    $totalCount = $database->fetch(
        "SELECT COUNT(*) as count FROM notifications WHERE {$whereClause}",
        $params
    )['count'];
    
    // Get counts by category and read status
    $counts = [
        'total' => $totalCount,
        'unread' => $database->fetch(
            "SELECT COUNT(*) as count FROM notifications 
             WHERE user_id = ? AND is_read = 0 AND (expires_at IS NULL OR expires_at > NOW())",
            [$userId]
        )['count'],
        'categories' => []
    ];
    
    // Get category counts
    $categoryCounts = $database->fetchAll(
        "SELECT category, 
                COUNT(*) as total,
                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread
         FROM notifications 
         WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
         GROUP BY category",
        [$userId]
    );
    
    foreach ($categoryCounts as $categoryCount) {
        $counts['categories'][$categoryCount['category']] = [
            'total' => $categoryCount['total'],
            'unread' => $categoryCount['unread']
        ];
    }
    
    // Format notifications
    $formattedNotifications = array_map('formatNotification', $notifications);
    
    echo json_encode([
        'success' => true,
        'data' => [
            'notifications' => $formattedNotifications,
            'counts' => $counts,
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => $totalCount,
                'has_more' => ($offset + $limit) < $totalCount
            ]
        ]
    ]);
}

function handleGetSingleNotification($notificationId, $userId) {
    global $database;

    // Get single notification
    $notification = $database->fetch(
        "SELECT * FROM notifications
         WHERE id = ? AND user_id = ? AND (expires_at IS NULL OR expires_at > NOW())",
        [$notificationId, $userId]
    );

    if (!$notification) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Notification not found'
        ]);
        return;
    }

    echo json_encode([
        'success' => true,
        'data' => formatNotification($notification)
    ]);
}

function handleUpdateNotification() {
    global $database;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $notificationId = $input['id'] ?? null;
    
    if (!$notificationId) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    
    // Check if notification exists and belongs to user
    $notification = $database->fetch(
        "SELECT * FROM notifications WHERE id = ? AND user_id = ?",
        [$notificationId, $userId]
    );
    
    if (!$notification) {
        http_response_code(404);
        echo json_encode(['error' => 'Notification not found']);
        return;
    }
    
    // Update notification
    $updateFields = [];
    $updateParams = [];
    
    if (isset($input['is_read'])) {
        $updateFields[] = 'is_read = ?';
        $updateParams[] = $input['is_read'] ? 1 : 0;
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['error' => 'No valid fields to update']);
        return;
    }
    
    $updateParams[] = $notificationId;
    
    $database->query(
        "UPDATE notifications SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
        $updateParams
    );
    
    echo json_encode(['success' => true]);
}

function handleDeleteNotification() {
    global $database;
    
    $notificationId = $_GET['id'] ?? null;
    
    if (!$notificationId) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    
    // Check if notification exists and belongs to user
    $notification = $database->fetch(
        "SELECT * FROM notifications WHERE id = ? AND user_id = ?",
        [$notificationId, $userId]
    );
    
    if (!$notification) {
        http_response_code(404);
        echo json_encode(['error' => 'Notification not found']);
        return;
    }
    
    // Delete notification
    $database->query("DELETE FROM notifications WHERE id = ?", [$notificationId]);
    
    echo json_encode(['success' => true]);
}

function formatNotification($notification) {
    // Parse metadata if it's JSON
    $metadata = null;
    if ($notification['metadata']) {
        $metadata = json_decode($notification['metadata'], true);
    }
    
    return [
        'id' => $notification['id'],
        'title' => $notification['title'],
        'message' => $notification['message'],
        'type' => $notification['type'],
        'category' => $notification['category'],
        'priority' => $notification['priority'],
        'is_read' => (bool)$notification['is_read'],
        'action_url' => $notification['action_url'],
        'metadata' => $metadata,
        'created_at' => $notification['created_at'],
        'expires_at' => $notification['expires_at'],
        'time_ago' => timeAgo($notification['created_at'])
    ];
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . 'm ago';
    if ($time < 86400) return floor($time/3600) . 'h ago';
    if ($time < 2592000) return floor($time/86400) . 'd ago';
    if ($time < 31536000) return floor($time/2592000) . 'mo ago';
    return floor($time/31536000) . 'y ago';
}
