<?php
/**
 * Test Flutterwave Inline Implementation
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Flutterwave Inline Implementation Test</h1>";

// Test 1: Configuration Check
echo "<h2>Test 1: Configuration Check</h2>";
echo "<p><strong>Flutterwave Enabled:</strong> " . (FLUTTERWAVE_ENABLED ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Public Key:</strong> " . (FLUTTERWAVE_PUBLIC_KEY ? substr(FLUTTERWAVE_PUBLIC_KEY, 0, 20) . '...' : 'Not set') . "</p>";
echo "<p><strong>Secret Key:</strong> " . (FLUTTERWAVE_SECRET_KEY ? substr(FLUTTERWAVE_SECRET_KEY, 0, 20) . '...' : 'Not set') . "</p>";

$isTestMode = strpos(FLUTTERWAVE_PUBLIC_KEY, 'FLWPUBK_TEST') === 0;
echo "<p><strong>Test Mode:</strong> " . ($isTestMode ? 'Yes' : 'No') . "</p>";

if (!FLUTTERWAVE_ENABLED) {
    echo "<p style='color: red;'>❌ Flutterwave is not enabled</p>";
    exit;
}

// Test 2: Check for eligible bookings
echo "<h2>Test 2: Eligible Bookings Check</h2>";

if (!isLoggedIn()) {
    echo "<p style='color: orange;'>⚠️ Please log in to test payments</p>";
    echo "<p><a href='/flix-php/auth/login'>Login here</a></p>";
    exit;
}

global $database;
$eligibleBookings = $database->fetchAll("
    SELECT b.*, s.name as service_name, pkg.name as package_name,
           p.id as payment_id, p.status as payment_status
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages pkg ON b.package_id = pkg.id
    LEFT JOIN payments p ON b.id = p.booking_id
    WHERE b.user_id = ? 
    AND b.status IN ('CONFIRMED', 'COMPLETED')
    AND (p.id IS NULL OR p.status IN ('PENDING', 'FAILED'))
    ORDER BY b.created_at DESC
    LIMIT 5
", [$_SESSION['user_id']]);

if (empty($eligibleBookings)) {
    echo "<p style='color: orange;'>⚠️ No eligible bookings found for testing</p>";
    echo "<p>Create a booking first to test payments</p>";
    exit;
}

echo "<p style='color: green;'>✅ Found " . count($eligibleBookings) . " eligible booking(s)</p>";

// Test 3: Inline Payment Test
echo "<h2>Test 3: Flutterwave Inline Payment Test</h2>";

$testBooking = $eligibleBookings[0];
$serviceName = $testBooking['service_name'] ?: $testBooking['package_name'] ?: 'Test Service';

echo "<div style='background: #f5f5f5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>Test Payment Details</h3>";
echo "<p><strong>Service:</strong> " . htmlspecialchars($serviceName) . "</p>";
echo "<p><strong>Amount:</strong> " . CURRENCY_SYMBOL . " " . number_format($testBooking['total_amount']) . "</p>";
echo "<p><strong>Booking ID:</strong> " . htmlspecialchars($testBooking['id']) . "</p>";

if ($isTestMode) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>Test Card Details</h4>";
    echo "<p style='margin: 5px 0; color: #856404;'><strong>Card Number:</strong> ****************</p>";
    echo "<p style='margin: 5px 0; color: #856404;'><strong>CVV:</strong> 828</p>";
    echo "<p style='margin: 5px 0; color: #856404;'><strong>Expiry:</strong> 09/32</p>";
    echo "<p style='margin: 5px 0; color: #856404;'><strong>PIN:</strong> 3310</p>";
    echo "<p style='margin: 5px 0; color: #856404;'><strong>OTP:</strong> 12345</p>";
    echo "</div>";
}

// Generate test transaction reference
$testTxRef = 'test_' . time() . '_' . substr($testBooking['id'], 0, 8);

echo "<button id='test-payment-btn' style='background: #f5a623; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
echo "<i class='fas fa-credit-card'></i> Test Flutterwave Inline Payment";
echo "</button>";

echo "</div>";

// Test 4: Implementation Details
echo "<h2>Test 4: Implementation Details</h2>";
echo "<ul>";
echo "<li>✅ Using Flutterwave Inline (v3.js)</li>";
echo "<li>✅ Supports test mode with test cards</li>";
echo "<li>✅ Handles callbacks and redirects</li>";
echo "<li>✅ Includes verification system</li>";
echo "<li>✅ Works with localhost development</li>";
echo "</ul>";

?>

<!-- Include Flutterwave Inline Script -->
<script src="https://checkout.flutterwave.com/v3.js"></script>

<script>
// Test Flutterwave Inline Payment
document.getElementById('test-payment-btn').addEventListener('click', function() {
    console.log('Starting Flutterwave Inline test payment...');
    
    const button = this;
    const originalText = button.innerHTML;
    
    // Show loading
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Initializing...';
    button.disabled = true;
    
    FlutterwaveCheckout({
        public_key: "<?= FLUTTERWAVE_PUBLIC_KEY ?>",
        tx_ref: "<?= $testTxRef ?>",
        amount: <?= $testBooking['total_amount'] ?>,
        currency: "<?= CURRENCY_CODE ?>",
        payment_options: "card,mobilemoney,ussd,banktransfer",
        redirect_url: "<?= getBaseUrl() ?>/test-flutterwave-return.php",
        customer: {
            email: "<?= htmlspecialchars($_SESSION['user_email'] ?? '<EMAIL>') ?>",
            name: "<?= htmlspecialchars($_SESSION['user_name'] ?? 'Test User') ?>",
            phone_number: "+************",
        },
        customizations: {
            title: "<?= htmlspecialchars(APP_NAME) ?> - Test",
            description: "Test payment for <?= htmlspecialchars($serviceName) ?>",
            logo: "<?= getBaseUrl() ?>/assets/images/logo.png",
        },
        callback: function (data) {
            console.log("Flutterwave callback received:", data);
            
            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;
            
            if (data.status === 'successful') {
                alert('✅ Test Payment Successful!\n\n' +
                      'Transaction ID: ' + data.transaction_id + '\n' +
                      'Reference: ' + data.tx_ref + '\n\n' +
                      'This was a test payment. Check the console for details.');
                console.log('Test payment successful:', data);
            } else if (data.status === 'cancelled') {
                alert('⚠️ Payment was cancelled by user');
            } else {
                alert('❌ Payment failed with status: ' + data.status);
                console.error('Payment failed:', data);
            }
        },
        onclose: function() {
            console.log("Flutterwave modal closed");
            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;
        }
    });
});

// Log test environment info
console.log('Flutterwave Test Environment Info:', {
    publicKey: '<?= FLUTTERWAVE_PUBLIC_KEY ?>',
    isTestMode: <?= $isTestMode ? 'true' : 'false' ?>,
    currency: '<?= CURRENCY_CODE ?>',
    testTxRef: '<?= $testTxRef ?>',
    amount: <?= $testBooking['total_amount'] ?>
});
</script>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p, li {
    margin: 10px 0;
}

.fas {
    margin-right: 5px;
}

button:hover {
    opacity: 0.9;
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<?php
echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Click the test payment button above</li>";
echo "<li>Use the provided test card details</li>";
echo "<li>Complete the payment flow</li>";
echo "<li>Check the browser console for detailed logs</li>";
echo "<li>If successful, try the real payment flow in your application</li>";
echo "</ol>";

echo "<p><strong>Benefits of Flutterwave Inline:</strong></p>";
echo "<ul>";
echo "<li>✅ Better testing support with test cards</li>";
echo "<li>✅ More reliable callback handling</li>";
echo "<li>✅ Improved user experience</li>";
echo "<li>✅ Better error handling</li>";
echo "<li>✅ Works seamlessly in development</li>";
echo "</ul>";
?>
