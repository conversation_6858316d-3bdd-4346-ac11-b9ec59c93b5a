<?php
/**
 * Test Stripe API Endpoint
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Stripe API Endpoint Test</h1>";

// Check if user is logged in
if (!isLoggedIn()) {
    echo "<p style='color: red;'>❌ Please log in first</p>";
    echo "<p><a href='/flix-php/auth/login'>Login here</a></p>";
    exit;
}

// Get a test payment
global $database;
$testPayment = $database->fetch("
    SELECT p.*, b.user_id 
    FROM payments p
    INNER JOIN bookings b ON p.booking_id = b.id
    WHERE p.payment_gateway = 'STRIPE' 
    AND b.user_id = ?
    ORDER BY p.created_at DESC 
    LIMIT 1
", [$_SESSION['user_id']]);

if (!$testPayment) {
    echo "<p style='color: orange;'>⚠️ No Stripe payments found for your account</p>";
    echo "<p>Create a booking and payment first</p>";
    exit;
}

echo "<h2>Test Payment Found</h2>";
echo "<p><strong>Payment ID:</strong> " . htmlspecialchars($testPayment['id']) . "</p>";
echo "<p><strong>Amount:</strong> " . CURRENCY_SYMBOL . " " . number_format($testPayment['amount']) . "</p>";
echo "<p><strong>Status:</strong> " . htmlspecialchars($testPayment['status']) . "</p>";

// Test the API endpoint
echo "<h2>Testing API Endpoint</h2>";

$testData = [
    'payment_id' => $testPayment['id'],
    'amount' => $testPayment['amount'],
    'currency' => strtolower(CURRENCY_CODE)
];

echo "<p><strong>Request Data:</strong></p>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";

// Make the API call
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => getBaseUrl() . '/api/payments/stripe/create-intent.php',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Cookie: ' . $_SERVER['HTTP_COOKIE'] ?? ''
    ],
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$error = curl_error($curl);
curl_close($curl);

echo "<h2>API Response</h2>";
echo "<p><strong>HTTP Code:</strong> " . $httpCode . "</p>";

if ($error) {
    echo "<p style='color: red;'><strong>cURL Error:</strong> " . htmlspecialchars($error) . "</p>";
} else {
    echo "<p style='color: green;'>✅ Request successful</p>";
}

echo "<p><strong>Response Body:</strong></p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars($response);
echo "</pre>";

// Try to parse JSON response
$jsonResponse = json_decode($response, true);
if ($jsonResponse) {
    echo "<p><strong>Parsed JSON:</strong></p>";
    echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars(json_encode($jsonResponse, JSON_PRETTY_PRINT));
    echo "</pre>";
    
    if (isset($jsonResponse['client_secret'])) {
        echo "<p style='color: green;'>✅ <strong>Client Secret Found:</strong> " . substr($jsonResponse['client_secret'], 0, 30) . "...</p>";
        echo "<p style='color: green;'>✅ API endpoint is working correctly!</p>";
    } else {
        echo "<p style='color: red;'>❌ No client_secret in response</p>";
    }
    
    if (isset($jsonResponse['error'])) {
        echo "<p style='color: red;'>❌ <strong>Error:</strong> " . htmlspecialchars($jsonResponse['error']) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Invalid JSON response</p>";
}

// Check server logs
echo "<h2>Debug Information</h2>";
echo "<p><strong>Session User ID:</strong> " . htmlspecialchars($_SESSION['user_id']) . "</p>";
echo "<p><strong>Payment User ID:</strong> " . htmlspecialchars($testPayment['user_id']) . "</p>";
echo "<p><strong>User Match:</strong> " . ($testPayment['user_id'] === $_SESSION['user_id'] ? '✅ Yes' : '❌ No') . "</p>";

echo "<p><strong>Stripe Configuration:</strong></p>";
echo "<ul>";
echo "<li>Enabled: " . (STRIPE_ENABLED ? '✅ Yes' : '❌ No') . "</li>";
echo "<li>Public Key: " . (STRIPE_PUBLIC_KEY ? '✅ Set' : '❌ Not set') . "</li>";
echo "<li>Secret Key: " . (STRIPE_SECRET_KEY ? '✅ Set' : '❌ Not set') . "</li>";
echo "</ul>";

echo "<hr>";
echo "<h2>Next Steps</h2>";

if ($httpCode === 200 && isset($jsonResponse['client_secret'])) {
    echo "<p style='color: green;'>✅ <strong>API is working correctly!</strong></p>";
    echo "<p>The Stripe payment form should now initialize properly.</p>";
    echo "<p><a href='/flix-php/customer/payments/stripe.php?payment_id=" . urlencode($testPayment['id']) . "'>Test Stripe Payment Form</a></p>";
} else {
    echo "<p style='color: red;'>❌ <strong>API has issues</strong></p>";
    echo "<p>Check the error message above and server logs for more details.</p>";
    echo "<p>Common issues:</p>";
    echo "<ul>";
    echo "<li>Payment not found (check user ID match)</li>";
    echo "<li>Stripe configuration issues</li>";
    echo "<li>Database connection problems</li>";
    echo "<li>Missing Stripe library (should use cURL fallback)</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

h1, h2 {
    color: #333;
}
</style>
