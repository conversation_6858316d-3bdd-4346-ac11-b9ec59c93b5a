<?php
/**
 * Staff Schedule API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_schedule_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];
$staffId = $_GET['staff_id'] ?? null;

if (!$staffId) {
    http_response_code(400);
    echo json_encode(['error' => 'Staff ID is required']);
    exit;
}

try {
    switch ($method) {
        case 'GET':
            handleGetStaffSchedule($staffId);
            break;
            
        case 'POST':
            handleUpdateStaffSchedule($staffId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetStaffSchedule($staffId) {
    global $database;
    
    try {
        // Get staff member details
        $staff = $database->fetch(
            "SELECT u.*, ss.role as staff_role, ss.hourly_rate, ss.schedule
             FROM users u
             LEFT JOIN staff_schedules ss ON u.id = ss.user_id
             WHERE u.id = ? AND u.role = 'STAFF'",
            [$staffId]
        );
        
        if (!$staff) {
            http_response_code(404);
            echo json_encode(['error' => 'Staff member not found']);
            return;
        }
        
        // Parse schedule or create default
        $schedule = [];
        if ($staff['schedule']) {
            $schedule = json_decode($staff['schedule'], true);
        }
        
        // Ensure all days are present with defaults
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        foreach ($days as $day) {
            if (!isset($schedule[$day])) {
                $schedule[$day] = [
                    'available' => false,
                    'start' => '09:00',
                    'end' => '18:00'
                ];
            }
        }
        
        // Calculate schedule statistics
        $workingDays = 0;
        $totalHours = 0;
        $hourlyRate = floatval($staff['hourly_rate'] ?? 50.00);
        
        foreach ($schedule as $daySchedule) {
            if ($daySchedule['available']) {
                $workingDays++;
                $start = strtotime($daySchedule['start']);
                $end = strtotime($daySchedule['end']);
                $hours = ($end - $start) / 3600; // Convert seconds to hours
                $totalHours += $hours;
            }
        }
        
        $weeklyCapacity = $totalHours * $hourlyRate;
        
        $stats = [
            'workingDays' => $workingDays,
            'totalHours' => number_format($totalHours, 1),
            'hourlyRate' => number_format($hourlyRate, 2),
            'weeklyCapacity' => number_format($weeklyCapacity, 2)
        ];
        
        echo json_encode([
            'success' => true,
            'data' => [
                'staff' => $staff,
                'schedule' => $schedule,
                'stats' => $stats
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch staff schedule: ' . $e->getMessage()]);
    }
}

function handleUpdateStaffSchedule($staffId) {
    global $database;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['schedule'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Schedule data is required']);
            return;
        }
        
        $schedule = $input['schedule'];
        
        // Validate schedule data
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        foreach ($days as $day) {
            if (!isset($schedule[$day])) {
                http_response_code(400);
                echo json_encode(['error' => "Missing schedule for $day"]);
                return;
            }
        }
        
        // Update schedule in database
        $result = updateStaffSchedule($staffId, $schedule);
        
        if ($result['success']) {
            echo json_encode([
                'success' => true,
                'message' => 'Schedule updated successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => $result['error']]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update staff schedule: ' . $e->getMessage()]);
    }
}
?>
