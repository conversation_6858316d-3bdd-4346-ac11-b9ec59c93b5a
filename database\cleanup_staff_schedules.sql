-- Cleanup Staff Schedules Table
-- Remove duplicate entries and ensure each staff member has only one record

USE flix_salonce2;

-- Step 1: Backup the current table (optional but recommended)
CREATE TABLE IF NOT EXISTS staff_schedules_backup AS 
SELECT * FROM staff_schedules;

-- Step 2: Create a temporary table with unique staff records
-- Keep the most recent record for each staff member
CREATE TEMPORARY TABLE staff_schedules_unique AS
SELECT 
    ss1.id,
    ss1.user_id,
    ss1.staff_id,
    ss1.day_of_week,
    ss1.start_time,
    ss1.end_time,
    ss1.is_working,
    ss1.role,
    ss1.hourly_rate,
    ss1.bio,
    ss1.experience,
    ss1.schedule,
    ss1.specialties,
    ss1.is_active,
    ss1.created_at,
    ss1.updated_at
FROM staff_schedules ss1
INNER JOIN (
    SELECT 
        user_id,
        MAX(updated_at) as latest_update
    FROM staff_schedules
    WHERE user_id IS NOT NULL
    GROUP BY user_id
) ss2 ON ss1.user_id = ss2.user_id AND ss1.updated_at = ss2.latest_update;

-- Step 3: Show what will be removed (for verification)
SELECT 
    'Duplicate records to be removed:' as action,
    COUNT(*) as count
FROM staff_schedules ss
WHERE ss.id NOT IN (SELECT id FROM staff_schedules_unique);

-- Step 4: Show which staff members have duplicates
SELECT 
    user_id,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id) as schedule_ids
FROM staff_schedules
WHERE user_id IS NOT NULL
GROUP BY user_id
HAVING COUNT(*) > 1;

-- Step 5: Remove duplicate records (keep only the unique ones)
DELETE FROM staff_schedules 
WHERE id NOT IN (SELECT id FROM staff_schedules_unique);

-- Step 6: Verify the cleanup
SELECT 
    'After cleanup - Records per staff member:' as verification,
    user_id,
    COUNT(*) as record_count
FROM staff_schedules
WHERE user_id IS NOT NULL
GROUP BY user_id
HAVING COUNT(*) > 1;

-- Step 7: Show final count
SELECT 
    'Total staff_schedules records after cleanup:' as result,
    COUNT(*) as total_records
FROM staff_schedules;

-- Step 8: Ensure all active staff have a schedule record
INSERT IGNORE INTO staff_schedules (id, user_id, role, hourly_rate, schedule, created_at, updated_at)
SELECT 
    CONCAT('schedule-', u.id) as id,
    u.id as user_id,
    'Staff Member' as role,
    500000 as hourly_rate,
    JSON_OBJECT(
        'monday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'tuesday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'wednesday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'thursday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'friday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'saturday', JSON_OBJECT('start_time', '10:00', 'end_time', '16:00', 'is_working', true),
        'sunday', JSON_OBJECT('start_time', '10:00', 'end_time', '16:00', 'is_working', false)
    ) as schedule,
    NOW() as created_at,
    NOW() as updated_at
FROM users u
WHERE u.role = 'STAFF' 
AND u.is_active = 1
AND NOT EXISTS (
    SELECT 1 FROM staff_schedules ss WHERE ss.user_id = u.id
);

-- Step 9: Add unique constraint to prevent future duplicates
ALTER TABLE staff_schedules 
ADD CONSTRAINT unique_user_schedule 
UNIQUE KEY (user_id);

-- Step 10: Final verification
SELECT 
    'Final verification - Staff with schedules:' as final_check,
    COUNT(DISTINCT u.id) as staff_count,
    COUNT(DISTINCT ss.user_id) as schedules_count
FROM users u
LEFT JOIN staff_schedules ss ON u.id = ss.user_id
WHERE u.role = 'STAFF' AND u.is_active = 1;
