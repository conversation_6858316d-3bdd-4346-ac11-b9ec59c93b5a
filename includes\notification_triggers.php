<?php
/**
 * Notification Triggers
 * Additional notification functions for specific use cases
 * Flix Salonce - PHP Version
 *
 * Note: Core notification functions are in functions.php
 * This file contains specialized notification triggers
 */

/**
 * Create staff-related notifications
 */
function createStaffNotification($staffId, $type, $additionalData = []) {
    global $database;

    // Get staff details
    $staff = $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
        [$staffId]
    );

    if (!$staff) {
        return false;
    }

    // Get all admin users
    $admins = $database->fetchAll("SELECT id FROM users WHERE role = 'ADMIN'");

    $notifications = [];

    switch ($type) {
        case 'STAFF_NEW':
            $title = 'New Staff Member';
            $message = "New staff member {$staff['name']} has been added to the system";
            $priority = 'MEDIUM';
            $actionUrl = getBasePath() . "/admin/staff/view.php?id={$staffId}";
            break;

        case 'STAFF_SCHEDULE_CHANGE':
            $title = 'Staff Schedule Change';
            $message = "Schedule has been updated for {$staff['name']}";
            $priority = 'MEDIUM';
            $actionUrl = getBasePath() . "/admin/staff/schedule.php?staff_id={$staffId}";
            break;

        case 'STAFF_LEAVE_REQUEST':
            $title = 'Staff Leave Request';
            $message = "{$staff['name']} has requested leave from {$additionalData['start_date']} to {$additionalData['end_date']}";
            $priority = 'HIGH';
            $actionUrl = getBasePath() . "/admin/staff/view.php?id={$staffId}";
            break;

        default:
            return false;
    }

    // Create notifications for all admins
    foreach ($admins as $admin) {
        $options = [
            'priority' => $priority,
            'action_url' => $actionUrl,
            'metadata' => array_merge([
                'staff_id' => $staffId
            ], $additionalData)
        ];

        $notificationId = createNotification($admin['id'], $title, $message, $type, $options);
        if ($notificationId) {
            $notifications[] = $notificationId;
        }
    }

    return $notifications;
}

/**
 * Create booking notifications for staff members
 */
function createStaffBookingNotification($bookingId, $type, $additionalData = []) {
    global $database;

    // Get booking details with staff information
    $booking = $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email,
                s.name as service_name, st.name as staff_name, st.id as staff_id,
                p.name as package_name
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         LEFT JOIN packages p ON b.package_id = p.id
         WHERE b.id = ?",
        [$bookingId]
    );

    if (!$booking || !$booking['staff_id']) {
        return false;
    }

    $customerName = $booking['customer_name'];
    $serviceName = $booking['service_name'] ?: $booking['package_name'];
    $staffId = $booking['staff_id'];

    $notifications = [
        'BOOKING_NEW' => [
            'title' => 'New Booking Assignment',
            'message' => "You have been assigned a new booking with {$customerName} for {$serviceName} on {$booking['date']} at {$booking['start_time']}",
            'priority' => 'HIGH'
        ],
        'BOOKING_CONFIRMED' => [
            'title' => 'Booking Confirmed',
            'message' => "Your booking with {$customerName} ({$serviceName}) has been confirmed for {$booking['date']} at {$booking['start_time']}",
            'priority' => 'MEDIUM'
        ],
        'BOOKING_CANCELLED' => [
            'title' => 'Booking Cancelled',
            'message' => "Booking with {$customerName} ({$serviceName}) on {$booking['date']} has been cancelled",
            'priority' => 'MEDIUM'
        ],
        'BOOKING_COMPLETED' => [
            'title' => 'Booking Completed',
            'message' => "Booking with {$customerName} ({$serviceName}) has been marked as completed",
            'priority' => 'LOW'
        ],
        'BOOKING_REMINDER' => [
            'title' => 'Upcoming Appointment',
            'message' => "Reminder: You have an appointment with {$customerName} ({$serviceName}) in 30 minutes",
            'priority' => 'HIGH'
        ],
        'BOOKING_EXPIRED' => [
            'title' => 'Booking Expired',
            'message' => "Booking with {$customerName} ({$serviceName}) on {$booking['date']} has expired",
            'priority' => 'LOW'
        ],
        'BOOKING_NO_SHOW' => [
            'title' => 'Customer No-Show',
            'message' => "Customer {$customerName} did not show up for {$serviceName} appointment on {$booking['date']}",
            'priority' => 'MEDIUM'
        ]
    ];

    if (!isset($notifications[$type])) return false;

    $notificationData = $notifications[$type];
    $metadata = array_merge([
        'booking_id' => $bookingId,
        'customer_name' => $customerName,
        'service_name' => $serviceName,
        'staff_name' => $booking['staff_name'],
        'booking_date' => $booking['date'],
        'booking_time' => $booking['start_time']
    ], $additionalData);

    $options = [
        'priority' => $notificationData['priority'],
        'action_url' => "/staff/appointments?date={$booking['date']}",
        'metadata' => $metadata
    ];

    // Create notification for the assigned staff member
    $notificationId = createNotification(
        $staffId,
        $notificationData['title'],
        $notificationData['message'],
        $type,
        $options
    );

    return $notificationId ? [$notificationId] : false;
}

/**
 * Create admin-triggered booking notifications for staff members
 */
function createAdminBookingNotificationForStaff($bookingId, $type, $adminId, $additionalData = []) {
    global $database;

    // Get booking details with staff information
    $booking = $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email,
                s.name as service_name, st.name as staff_name, st.id as staff_id,
                p.name as package_name, admin.name as admin_name
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         LEFT JOIN packages p ON b.package_id = p.id
         LEFT JOIN users admin ON admin.id = ? AND admin.role = 'ADMIN'
         WHERE b.id = ?",
        [$adminId, $bookingId]
    );

    if (!$booking) {
        return false;
    }

    // For unassignment notifications, use the old staff ID from additional data
    $targetStaffId = $booking['staff_id'];
    if ($type === 'ADMIN_STAFF_UNASSIGNED' && isset($additionalData['old_staff_id'])) {
        $targetStaffId = $additionalData['old_staff_id'];
        // Get the old staff member's name
        $oldStaff = $database->fetch("SELECT name FROM users WHERE id = ? AND role = 'STAFF'", [$targetStaffId]);
        if (!$oldStaff) {
            return false;
        }
        $booking['staff_name'] = $oldStaff['name'];
    }

    if (!$targetStaffId) {
        return false;
    }

    $customerName = $booking['customer_name'];
    $serviceName = $booking['service_name'] ?: $booking['package_name'];
    $staffId = $targetStaffId;
    $adminName = $booking['admin_name'] ?: 'Admin';

    $notifications = [
        'ADMIN_STAFF_ASSIGNED' => [
            'title' => 'New Assignment from Admin',
            'message' => "Admin {$adminName} has assigned you to a booking with {$customerName} for {$serviceName} on {$booking['date']} at {$booking['start_time']}",
            'priority' => 'HIGH'
        ],
        'ADMIN_STAFF_UNASSIGNED' => [
            'title' => 'Assignment Removed by Admin',
            'message' => "Admin {$adminName} has removed you from the booking with {$customerName} for {$serviceName} on {$booking['date']}",
            'priority' => 'MEDIUM'
        ],
        'ADMIN_BOOKING_UPDATED' => [
            'title' => 'Booking Updated by Admin',
            'message' => "Admin {$adminName} has updated your booking with {$customerName} for {$serviceName}",
            'priority' => 'MEDIUM'
        ],
        'ADMIN_STATUS_CHANGED' => [
            'title' => 'Booking Status Changed by Admin',
            'message' => "Admin {$adminName} has changed the status of your booking with {$customerName} ({$serviceName}) to {$booking['status']}",
            'priority' => 'MEDIUM'
        ],
        'ADMIN_BOOKING_RESCHEDULED' => [
            'title' => 'Booking Rescheduled by Admin',
            'message' => "Admin {$adminName} has rescheduled your booking with {$customerName} ({$serviceName})",
            'priority' => 'HIGH'
        ],
        'ADMIN_BOOKING_DETAILS_CHANGED' => [
            'title' => 'Booking Details Modified by Admin',
            'message' => "Admin {$adminName} has modified details for your booking with {$customerName}",
            'priority' => 'MEDIUM'
        ]
    ];

    if (!isset($notifications[$type])) return false;

    $notificationData = $notifications[$type];

    // Add specific change details to the message if provided
    if (isset($additionalData['changes']) && !empty($additionalData['changes'])) {
        $changeDetails = [];
        foreach ($additionalData['changes'] as $field => $change) {
            switch ($field) {
                case 'date':
                    $changeDetails[] = "Date changed from {$change['old']} to {$change['new']}";
                    break;
                case 'start_time':
                    $changeDetails[] = "Time changed from {$change['old']} to {$change['new']}";
                    break;
                case 'service':
                    $changeDetails[] = "Service changed from {$change['old']} to {$change['new']}";
                    break;
                case 'staff':
                    $changeDetails[] = "Staff assignment changed from {$change['old']} to {$change['new']}";
                    break;
                case 'status':
                    $changeDetails[] = "Status changed from {$change['old']} to {$change['new']}";
                    break;
                default:
                    $changeDetails[] = ucfirst($field) . " changed";
            }
        }

        if (!empty($changeDetails)) {
            $notificationData['message'] .= ". Changes: " . implode(', ', $changeDetails);
        }
    }

    $metadata = array_merge([
        'booking_id' => $bookingId,
        'customer_name' => $customerName,
        'service_name' => $serviceName,
        'staff_name' => $booking['staff_name'],
        'admin_name' => $adminName,
        'booking_date' => $booking['date'],
        'booking_time' => $booking['start_time'],
        'admin_triggered' => true
    ], $additionalData);

    $options = [
        'priority' => $notificationData['priority'],
        'action_url' => "/staff/appointments?date={$booking['date']}",
        'metadata' => $metadata
    ];

    // Create notification for the assigned staff member
    $notificationId = createNotification(
        $staffId,
        $notificationData['title'],
        $notificationData['message'],
        $type,
        $options
    );

    return $notificationId ? [$notificationId] : false;
}

/**
 * Create payment-related notifications
 */
function createPaymentNotification($paymentId, $type, $additionalData = []) {
    global $database;
    
    // Get all admin users
    $admins = $database->fetchAll("SELECT id FROM users WHERE role = 'ADMIN'");
    
    $notifications = [];
    
    switch ($type) {
        case 'PAYMENT_SUCCESS':
            $title = 'Payment Successful';
            $message = "Payment of {$additionalData['amount']} has been processed successfully";
            $priority = 'LOW';
            $actionUrl = getBasePath() . "/admin/earnings";
            break;
            
        case 'PAYMENT_FAILED':
            $title = 'Payment Failed';
            $message = "Payment of {$additionalData['amount']} has failed. Reason: {$additionalData['reason']}";
            $priority = 'HIGH';
            $actionUrl = getBasePath() . "/admin/bookings";
            break;
            
        case 'REFUND_PROCESSED':
            $title = 'Refund Processed';
            $message = "Refund of {$additionalData['amount']} has been processed for booking #{$additionalData['booking_id']}";
            $priority = 'MEDIUM';
            $actionUrl = getBasePath() . "/admin/earnings";
            break;
            
        default:
            return false;
    }
    
    // Create notifications for all admins
    foreach ($admins as $admin) {
        $options = [
            'priority' => $priority,
            'action_url' => $actionUrl,
            'metadata' => array_merge([
                'payment_id' => $paymentId
            ], $additionalData)
        ];
        
        $notificationId = createNotification($admin['id'], $title, $message, $type, $options);
        if ($notificationId) {
            $notifications[] = $notificationId;
        }
    }
    
    return $notifications;
}

/**
 * Alias for createSystemNotification for backward compatibility
 */
function createSystemNotificationForAdmins($type, $message, $priority = 'MEDIUM', $additionalData = []) {
    return createSystemNotification($type, $message, $priority, $additionalData);
}

/**
 * Check for booking conflicts and create notifications
 */
function checkBookingConflicts($bookingId) {
    global $database;
    
    $booking = $database->fetch(
        "SELECT * FROM bookings WHERE id = ?",
        [$bookingId]
    );
    
    if (!$booking) {
        return false;
    }
    
    // Check for overlapping bookings with the same staff
    $conflicts = $database->fetchAll(
        "SELECT b.*, u.name as customer_name 
         FROM bookings b
         JOIN users u ON b.user_id = u.id
         WHERE b.staff_id = ? 
         AND b.date = ? 
         AND b.id != ?
         AND b.status IN ('PENDING', 'CONFIRMED')
         AND (
             (b.time <= ? AND DATE_ADD(STR_TO_DATE(CONCAT(b.date, ' ', b.time), '%Y-%m-%d %H:%i:%s'), INTERVAL b.duration MINUTE) > ?)
             OR (? <= b.time AND DATE_ADD(STR_TO_DATE(CONCAT(?, ' ', ?), '%Y-%m-%d %H:%i:%s'), INTERVAL ? MINUTE) > b.time)
         )",
        [
            $booking['staff_id'],
            $booking['date'],
            $bookingId,
            $booking['time'],
            $booking['time'],
            $booking['time'],
            $booking['date'],
            $booking['time'],
            $booking['duration']
        ]
    );
    
    if (!empty($conflicts)) {
        $conflictList = implode(', ', array_map(function($c) {
            return "#{$c['id']} ({$c['customer_name']})";
        }, $conflicts));
        
        return createSystemNotification(
            'STAFF_SCHEDULE_CHANGE',
            "Booking conflict detected for booking #{$bookingId}. Conflicting bookings: {$conflictList}",
            'URGENT',
            [
                'booking_id' => $bookingId,
                'conflicts' => $conflicts
            ]
        );
    }
    
    return [];
}

/**
 * Enhanced booking notification with email integration
 */
function createBookingNotificationWithEmail($bookingId, $type, $additionalData = []) {
    // Create the regular notification
    $notificationResult = createBookingNotification($bookingId, $type, $additionalData);

    // Send email notifications based on type
    switch ($type) {
        case 'BOOKING_NEW':
            // Send confirmation email to customer
            sendBookingConfirmationEmail($bookingId);
            break;

        case 'BOOKING_CONFIRMED':
            // Send confirmation email to customer
            sendBookingConfirmationEmail($bookingId);
            // Notify assigned staff
            sendStaffBookingNotificationEmail($bookingId, $type);
            break;

        case 'BOOKING_CANCELLED':
            // Send cancellation email to customer
            $reason = $additionalData['reason'] ?? '';
            sendBookingCancellationEmail($bookingId, $reason);
            // Notify assigned staff
            sendStaffBookingNotificationEmail($bookingId, $type);
            break;

        case 'BOOKING_REMINDER':
            // Send reminder email to customer
            sendBookingReminderEmail($bookingId);
            break;
    }

    return $notificationResult;
}

/**
 * Enhanced staff notification with email integration
 */
function createStaffNotificationWithEmail($staffId, $type, $additionalData = []) {
    // Create the regular notification
    $notificationResult = createStaffNotification($staffId, $type, $additionalData);

    // Send email notification to staff
    global $database;

    $staff = $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
        [$staffId]
    );

    if ($staff && $staff['email']) {
        $emailSubject = '';
        $emailMessage = '';

        switch ($type) {
            case 'STAFF_NEW':
                $emailSubject = 'Welcome to ' . APP_NAME . ' Team!';
                $emailMessage = 'Welcome to our team! You have been added as a staff member. Please log in to access your staff panel and manage your schedule.';
                break;

            case 'STAFF_SCHEDULE_CHANGE':
                $emailSubject = 'Schedule Update - ' . APP_NAME;
                $emailMessage = 'Your schedule has been updated. Please check your staff panel to view the latest changes.';
                break;

            case 'STAFF_LEAVE_REQUEST':
                $emailSubject = 'Leave Request Submitted - ' . APP_NAME;
                $emailMessage = 'Your leave request has been submitted and is pending approval. You will be notified once it has been reviewed.';
                break;
        }

        if ($emailSubject && $emailMessage) {
            sendStaffNotificationEmail($staffId, $emailSubject, $emailMessage);
        }
    }

    return $notificationResult;
}

/**
 * Send booking reminder emails (to be called by cron job)
 */
function sendBookingReminders() {
    global $database;

    $results = [
        '24_hours' => 0,
        '5_hours' => 0,
        '30_minutes' => 0,
        'at_time' => 0
    ];

    // 24 hours before
    $bookings24h = $database->fetchAll(
        "SELECT id FROM bookings
         WHERE status IN ('CONFIRMED', 'PENDING')
         AND CONCAT(date, ' ', start_time) BETWEEN NOW() + INTERVAL 23 HOUR 30 MINUTE AND NOW() + INTERVAL 24 HOUR 30 MINUTE
         AND id NOT IN (SELECT booking_id FROM email_logs WHERE subject LIKE '%Reminder%' AND created_at > DATE_SUB(NOW(), INTERVAL 25 HOUR))"
    );

    foreach ($bookings24h as $booking) {
        if (sendBookingReminderEmail($booking['id'], '24_hours')) {
            $results['24_hours']++;
        }
    }

    // 5 hours before
    $bookings5h = $database->fetchAll(
        "SELECT id FROM bookings
         WHERE status IN ('CONFIRMED', 'PENDING')
         AND CONCAT(date, ' ', start_time) BETWEEN NOW() + INTERVAL 4 HOUR 30 MINUTE AND NOW() + INTERVAL 5 HOUR 30 MINUTE
         AND id NOT IN (SELECT booking_id FROM email_logs WHERE subject LIKE '%Reminder%' AND created_at > DATE_SUB(NOW(), INTERVAL 6 HOUR))"
    );

    foreach ($bookings5h as $booking) {
        if (sendBookingReminderEmail($booking['id'], '5_hours')) {
            $results['5_hours']++;
        }
    }

    // 30 minutes before
    $bookings30m = $database->fetchAll(
        "SELECT id FROM bookings
         WHERE status IN ('CONFIRMED', 'PENDING')
         AND CONCAT(date, ' ', start_time) BETWEEN NOW() + INTERVAL 25 MINUTE AND NOW() + INTERVAL 35 MINUTE
         AND id NOT IN (SELECT booking_id FROM email_logs WHERE subject LIKE '%Reminder%' AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR))"
    );

    foreach ($bookings30m as $booking) {
        if (sendBookingReminderEmail($booking['id'], '30_minutes')) {
            $results['30_minutes']++;
        }
    }

    // At appointment time
    $bookingsNow = $database->fetchAll(
        "SELECT id FROM bookings
         WHERE status IN ('CONFIRMED', 'PENDING')
         AND CONCAT(date, ' ', start_time) BETWEEN NOW() - INTERVAL 5 MINUTE AND NOW() + INTERVAL 5 MINUTE
         AND id NOT IN (SELECT booking_id FROM email_logs WHERE subject LIKE '%Appointment Time%' AND created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE))"
    );

    foreach ($bookingsNow as $booking) {
        if (sendBookingReminderEmail($booking['id'], 'at_time')) {
            $results['at_time']++;
        }
    }

    return $results;
}
