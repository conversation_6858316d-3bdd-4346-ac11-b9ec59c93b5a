<?php
/**
 * Admin Staff Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Require admin authentication
$auth->requireRole('ADMIN');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createStaffMember($_POST);
            if ($result['success']) {
                $_SESSION['staff_success'] = 'Staff member created successfully!';
            } else {
                $_SESSION['staff_error'] = $result['error'];
            }
            break;

        case 'update':
            $result = updateStaffMember($_POST['id'], $_POST);
            if ($result['success']) {
                $_SESSION['staff_success'] = 'Staff member updated successfully!';
            } else {
                $_SESSION['staff_error'] = $result['error'];
            }
            break;

        case 'toggle_status':
            $result = toggleStaffStatus($_POST['id']);
            if ($result['success']) {
                $_SESSION['staff_success'] = 'Staff status updated successfully!';
            } else {
                $_SESSION['staff_error'] = $result['error'];
            }
            break;


    }
    
    redirect('/admin/staff');
}

// Get services for specialties
$services = $database->fetchAll("SELECT id, name FROM services WHERE is_active = 1 ORDER BY name ASC");

// Get staff members with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 12;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? '');

$whereClause = "WHERE u.role = 'STAFF'";
$params = [];

if ($search) {
    $whereClause .= " AND (u.name LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status !== '') {
    $whereClause .= " AND u.is_active = ?";
    $params[] = (int)$status;
}

$staff = $database->fetchAll(
    "SELECT u.*,
            ss.role as staff_role,
            ss.hourly_rate,
            COUNT(DISTINCT b.id) as total_bookings,
            COUNT(DISTINCT CASE WHEN b.status = 'COMPLETED' THEN b.id END) as completed_bookings,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_revenue
     FROM users u
     LEFT JOIN staff_schedules ss ON u.id = ss.user_id
     LEFT JOIN bookings b ON u.id = b.staff_id
     $whereClause
     GROUP BY u.id, ss.role, ss.hourly_rate
     ORDER BY u.created_at DESC
     LIMIT $limit OFFSET $offset",
    $params
);

$totalStaff = $database->fetch(
    "SELECT COUNT(*) as count FROM users u $whereClause",
    $params
)['count'];

$totalPages = ceil($totalStaff / $limit);

// Get staff statistics
$stats = getStaffStats();

// Handle messages - only for staff page
$message = '';
$messageType = '';
if (isset($_SESSION['staff_success'])) {
    $message = $_SESSION['staff_success'];
    $messageType = 'success';
    unset($_SESSION['staff_success']);
} elseif (isset($_SESSION['staff_error'])) {
    $message = $_SESSION['staff_error'];
    $messageType = 'error';
    unset($_SESSION['staff_error']);
}

$pageTitle = "Staff Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-white">Staff Management</h1>
                            <p class="mt-1 text-sm text-gray-300">Manage your salon staff, schedules, and permissions</p>
                        </div>
                        <div class="mt-4 sm:mt-0 flex gap-2">
                            <!-- Sync Schedules button removed - schedules are now managed directly -->
                            <button onclick="openCreateModal()" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Staff Member
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message Display -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Total Staff</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['total']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Active Staff</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['active']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Avg. Monthly Revenue</dt>
                                    <dd class="text-lg font-medium text-white"><?= formatCurrency($stats['avg_monthly_revenue']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Total Bookings</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['total_bookings']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <form method="GET" class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="Search staff by name, email, or phone..." 
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        <div>
                            <select name="status" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Status</option>
                                <option value="1" <?= $status === '1' ? 'selected' : '' ?>>Active</option>
                                <option value="0" <?= $status === '0' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Filter
                        </button>
                        <?php if ($search || $status !== ''): ?>
                            <a href="<?= getBasePath() ?>/admin/staff" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                Clear
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Staff Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    <?php foreach ($staff as $member): ?>
                        <div class="bg-secondary-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                            <div class="p-6">
                                <div class="flex items-center mb-4">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <div class="h-12 w-12 rounded-full bg-salon-gold flex items-center justify-center">
                                            <span class="text-lg font-medium text-black">
                                                <?= strtoupper(substr($member['name'], 0, 2)) ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <h3 class="text-lg font-semibold text-white"><?= htmlspecialchars($member['name']) ?></h3>
                                        <p class="text-sm text-gray-300"><?= htmlspecialchars($member['email']) ?></p>
                                        <?php if ($member['staff_role']): ?>
                                            <p class="text-xs text-salon-gold font-medium"><?= htmlspecialchars($member['staff_role']) ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $member['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                            <?= $member['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </div>
                                </div>

                                <?php if ($member['phone']): ?>
                                    <div class="mb-3">
                                        <p class="text-sm text-gray-300">
                                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            <?= htmlspecialchars($member['phone']) ?>
                                        </p>
                                    </div>
                                <?php endif; ?>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-white"><?= number_format($member['completed_bookings']) ?></div>
                                        <div class="text-xs text-gray-400">Completed</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-salon-gold">TSH <?= number_format($member['total_revenue'], 0) ?></div>
                                        <div class="text-xs text-gray-400">Total Revenue</div>
                                    </div>
                                </div>

                                <div class="text-center text-xs text-gray-400 mb-4">
                                    Joined <?= date('M j, Y', strtotime($member['created_at'])) ?>
                                </div>

                                <div class="flex gap-2 mb-2">
                                    <button onclick="editStaff('<?= $member['id'] ?>')"
                                            class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                        Edit
                                    </button>
                                    <a href="<?= getBasePath() ?>/admin/staff/schedule.php?id=<?= $member['id'] ?>"
                                       class="flex-1 bg-purple-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors text-center">
                                        Schedule
                                    </a>
                                </div>
                                <div class="flex gap-2">
                                    <button onclick="viewCalendar('<?= $member['id'] ?>', '<?= htmlspecialchars($member['name']) ?>')"
                                            class="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors">
                                        Calendar
                                    </button>
                                    <button onclick="toggleStatus('<?= $member['id'] ?>', <?= $member['is_active'] ? 'false' : 'true' ?>)"
                                            class="flex-1 <?= $member['is_active'] ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' ?> text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                        <?= $member['is_active'] ? 'Deactivate' : 'Activate' ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="bg-secondary-800 px-4 py-3 flex items-center justify-between border-t border-secondary-700 sm:px-6 rounded-lg">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-300">
                                    Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalStaff) ?></span> of <span class="font-medium"><?= $totalStaff ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create/Edit Staff Modal -->
<div id="staffModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="modalTitle" class="text-xl font-bold text-white">Add Staff Member</h2>
            <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="staffForm" method="POST">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="id" id="staffId">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Full Name *</label>
                    <input type="text" name="name" id="staffName" required 
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Email *</label>
                    <input type="email" name="email" id="staffEmail" required 
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Phone</label>
                    <input type="tel" name="phone" id="staffPhone"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Staff Role</label>
                    <select name="staff_role" id="staffRole"
                            class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="Staff Member">Staff Member</option>
                        <option value="Senior Stylist">Senior Stylist</option>
                        <option value="Hair Stylist">Hair Stylist</option>
                        <option value="Massage Therapist">Massage Therapist</option>
                        <option value="Esthetician">Esthetician</option>
                        <option value="Nail Technician">Nail Technician</option>
                        <option value="Manager">Manager</option>
                        <option value="Assistant Manager">Assistant Manager</option>
                        <option value="Specialist">Specialist</option>
                        <option value="Trainee">Trainee</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Password *</label>
                    <input type="password" name="password" id="staffPassword"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    <p class="text-xs text-gray-400 mt-1">Leave blank to keep current password (when editing)</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Salary (TSH)</label>
                    <input type="number" name="hourly_rate" id="staffHourlyRate" step="1000" min="0" placeholder="e.g. 500000"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Services Specialties</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                    <?php foreach ($services as $service): ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="specialties[]" value="<?= $service['id'] ?>"
                                   class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                            <span class="ml-2 text-sm text-gray-300"><?= htmlspecialchars($service['name']) ?></span>
                        </label>
                    <?php endforeach; ?>
                </div>
                <p class="text-xs text-gray-400 mt-1">Select the services this staff member can perform</p>
            </div>
            
            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" id="staffActive" value="1" checked 
                           class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                    <span class="ml-2 text-sm text-gray-300">Staff member is active</span>
                </label>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Save Staff Member
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Schedule Modal - Removed: Now using dedicated schedule page -->

<!-- Calendar Modal -->
<div id="calendarModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-6xl mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="calendarModalTitle" class="text-xl font-bold text-white">Staff Calendar</h2>
            <button onclick="closeCalendarModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Calendar Stats -->
        <div id="calendarStats" class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <!-- Stats will be loaded here -->
        </div>

        <!-- Calendar Navigation -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex gap-2">
                <button onclick="changeCalendarMonth(-1)" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    ← Previous
                </button>
                <button onclick="changeCalendarMonth(1)" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Next →
                </button>
            </div>
            <div class="text-white font-semibold" id="calendarCurrentMonth">
                <!-- Current month will be displayed here -->
            </div>
            <button onclick="goToToday()" class="bg-salon-gold text-black px-3 py-2 rounded-lg hover:bg-gold-light transition-colors">
                Today
            </button>
        </div>

        <!-- Calendar Content -->
        <div id="calendarContent" class="bg-secondary-700 rounded-lg p-4 min-h-96">
            <!-- Calendar will be loaded here -->
        </div>

        <input type="hidden" id="calendarStaffId">
        <input type="hidden" id="currentCalendarDate" value="<?= date('Y-m-01') ?>">
    </div>
</div>

<!-- Booking Detail Modal -->
<div id="bookingDetailModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70]">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Booking Details</h3>
            <button onclick="closeBookingDetailModal()" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="bookingDetailContent">
            <!-- Booking details will be loaded here -->
        </div>
    </div>
</div>

<script>
// Modal functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add Staff Member';
    document.getElementById('formAction').value = 'create';
    document.getElementById('staffForm').reset();
    document.getElementById('staffActive').checked = true;
    document.getElementById('staffPassword').required = true;

    // Reset staff role to default
    const staffRoleSelect = document.getElementById('staffRole');
    if (staffRoleSelect) {
        staffRoleSelect.value = 'Staff Member';
    }

    // Reset salary to default
    const staffHourlyRate = document.getElementById('staffHourlyRate');
    if (staffHourlyRate) {
        staffHourlyRate.value = '500000';
    }

    document.getElementById('staffModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('staffModal').classList.add('hidden');
}

function editStaff(staffId) {
    console.log('Edit staff called with ID:', staffId);

    // Show loading state
    document.getElementById('modalTitle').textContent = 'Loading Staff Data...';
    document.getElementById('staffModal').classList.remove('hidden');

    // Try multiple API endpoints as fallback
    const apiEndpoints = [
        `<?= getBasePath() ?>/api/admin/get-staff-direct.php?staff_id=${staffId}`,
        `<?= getBasePath() ?>/api/admin/staff/${staffId}`,
        `<?= getBasePath() ?>/api/admin/staff.php?id=${staffId}`,
        `<?= getBasePath() ?>/api/admin/staff.php/${staffId}`
    ];

    let currentEndpoint = 0;

    function tryNextEndpoint() {
        if (currentEndpoint >= apiEndpoints.length) {
            console.error('All API endpoints failed');
            alert('Error: Unable to load staff data from any endpoint');
            document.getElementById('staffModal').classList.add('hidden');
            return;
        }

        const endpoint = apiEndpoints[currentEndpoint];
        console.log(`Trying endpoint ${currentEndpoint + 1}:`, endpoint);

        fetch(endpoint)
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text(); // Get as text first to debug
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed data:', data);

                    if (data.success && data.data) {
                        populateStaffForm(data.data);
                    } else {
                        console.error('API returned error:', data.error || 'Unknown error');
                        currentEndpoint++;
                        tryNextEndpoint();
                    }
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response text:', text);
                    currentEndpoint++;
                    tryNextEndpoint();
                }
            })
            .catch(error => {
                console.error(`Endpoint ${currentEndpoint + 1} failed:`, error);
                currentEndpoint++;
                tryNextEndpoint();
            });
    }

    // Start trying endpoints
    tryNextEndpoint();
}

function populateStaffForm(staff) {
    console.log('Populating form with staff data:', staff);

    try {
        // Set modal title
        document.getElementById('modalTitle').textContent = 'Edit Staff Member';
        document.getElementById('formAction').value = 'update';

        // Populate basic fields with null checks
        document.getElementById('staffId').value = staff.id || '';
        document.getElementById('staffName').value = staff.name || '';
        document.getElementById('staffEmail').value = staff.email || '';
        document.getElementById('staffPhone').value = staff.phone || '';
        document.getElementById('staffPassword').value = '';
        document.getElementById('staffPassword').required = false;

        // Set staff role
        const staffRoleSelect = document.getElementById('staffRole');
        if (staffRoleSelect && staff.staff_role) {
            staffRoleSelect.value = staff.staff_role;
        }

        // Set salary
        const staffHourlyRate = document.getElementById('staffHourlyRate');
        if (staffHourlyRate && staff.hourly_rate) {
            staffHourlyRate.value = Math.round(staff.hourly_rate);
        }

        // Handle boolean values properly
        const isActiveCheckbox = document.getElementById('staffActive');
        if (isActiveCheckbox) {
            isActiveCheckbox.checked = Boolean(staff.is_active);
        }

        // Clear all specialties first
        const specialtyCheckboxes = document.querySelectorAll('input[name="specialties[]"]');
        specialtyCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Handle specialties with robust checking
        const specialties = staff.specialties || [];
        console.log('Processing specialties:', specialties);

        if (Array.isArray(specialties)) {
            specialties.forEach(serviceId => {
                console.log('Looking for checkbox with value:', serviceId);
                const checkbox = document.querySelector(`input[name="specialties[]"][value="${serviceId}"]`);
                if (checkbox) {
                    console.log('Found and checking checkbox for service:', serviceId);
                    checkbox.checked = true;
                } else {
                    console.warn('Checkbox not found for service ID:', serviceId);
                }
            });
        } else {
            console.warn('Specialties is not an array:', specialties);
        }

        // Log final form state
        console.log('Form populated successfully');
        console.log('Checked specialties:', Array.from(document.querySelectorAll('input[name="specialties[]"]:checked')).map(cb => cb.value));

        // Validate that required fields are populated
        const requiredFields = ['staffName', 'staffEmail'];
        let allFieldsPopulated = true;

        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (!field || !field.value.trim()) {
                console.warn(`Required field ${fieldId} is empty`);
                allFieldsPopulated = false;
            }
        });

        if (!allFieldsPopulated) {
            console.warn('Some required fields are empty, but form is still populated');
        }

    } catch (error) {
        console.error('Error populating form:', error);
        alert('Error populating form: ' + error.message);
    }
}

// Add form submission validation
function validateStaffForm() {
    const name = document.getElementById('staffName').value.trim();
    const email = document.getElementById('staffEmail').value.trim();

    if (!name) {
        alert('Staff name is required');
        return false;
    }

    if (!email) {
        alert('Staff email is required');
        return false;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('Please enter a valid email address');
        return false;
    }

    return true;
}

function toggleStatus(staffId, newStatus) {
    const action = newStatus ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this staff member?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_status">
            <input type="hidden" name="id" value="${staffId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Schedule functions removed - now using dedicated schedule page





// Calendar functions
function viewCalendar(staffId, staffName) {
    document.getElementById('calendarModalTitle').textContent = `Calendar - ${staffName}`;
    document.getElementById('calendarStaffId').value = staffId;

    // Set current date to today
    const today = new Date();
    const currentDate = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-01';
    document.getElementById('currentCalendarDate').value = currentDate;

    // Load calendar data
    loadCalendarData(staffId, currentDate);

    document.getElementById('calendarModal').classList.remove('hidden');
}

function closeCalendarModal() {
    document.getElementById('calendarModal').classList.add('hidden');
}

function loadCalendarData(staffId, date) {
    const startDate = date;
    const endDate = new Date(date);
    endDate.setMonth(endDate.getMonth() + 1);
    endDate.setDate(0); // Last day of the month
    const endDateStr = endDate.getFullYear() + '-' + String(endDate.getMonth() + 1).padStart(2, '0') + '-' + String(endDate.getDate()).padStart(2, '0');

    // Update current month display
    const monthNames = ["January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"];
    const dateObj = new Date(date);
    document.getElementById('calendarCurrentMonth').textContent =
        monthNames[dateObj.getMonth()] + ' ' + dateObj.getFullYear();

    console.log('🔄 Loading calendar data for staff:', staffId, 'date:', date);

    // Fetch calendar data
    fetch(`<?= getBasePath() ?>/api/admin/staff-bookings.php?staff_id=${staffId}&start_date=${startDate}&end_date=${endDateStr}`)
        .then(response => {
            console.log('📡 API Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📊 API Response data:', data);

            if (data.success) {
                displayCalendarStats(data.data.stats);
                displayCalendar(data.data.events, date);

                // Show message if staff not found but API still returned success
                if (data.message) {
                    console.warn('⚠️ API Message:', data.message);
                    if (data.available_staff && data.available_staff.length > 0) {
                        console.log('📋 Available staff:', data.available_staff);
                    }
                }
            } else {
                console.error('❌ Error loading calendar data:', data.error);
                document.getElementById('calendarContent').innerHTML = `
                    <div class="text-center text-red-400 py-8">
                        <p>Error loading calendar data</p>
                        <p class="text-sm mt-2">${data.error || 'Unknown error'}</p>
                        <button onclick="loadCalendarData('${staffId}', '${date}')"
                                class="mt-4 bg-salon-gold text-black px-4 py-2 rounded-lg hover:bg-gold-light transition-colors">
                            Retry
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('❌ Network error:', error);
            document.getElementById('calendarContent').innerHTML = `
                <div class="text-center text-red-400 py-8">
                    <p>Network error loading calendar data</p>
                    <p class="text-sm mt-2">${error.message}</p>
                    <button onclick="loadCalendarData('${staffId}', '${date}')"
                            class="mt-4 bg-salon-gold text-black px-4 py-2 rounded-lg hover:bg-gold-light transition-colors">
                        Retry
                    </button>
                </div>
            `;
        });
}

function displayCalendarStats(stats) {
    document.getElementById('calendarStats').innerHTML = `
        <div class="bg-secondary-700 rounded-lg p-3 text-center">
            <div class="text-lg font-semibold text-white">${stats.total_bookings}</div>
            <div class="text-xs text-gray-400">Total</div>
        </div>
        <div class="bg-blue-600 rounded-lg p-3 text-center">
            <div class="text-lg font-semibold text-white">${stats.confirmed_bookings}</div>
            <div class="text-xs text-white">Confirmed</div>
        </div>
        <div class="bg-green-600 rounded-lg p-3 text-center">
            <div class="text-lg font-semibold text-white">${stats.completed_bookings}</div>
            <div class="text-xs text-white">Completed</div>
        </div>
        <div class="bg-yellow-600 rounded-lg p-3 text-center">
            <div class="text-lg font-semibold text-white">${stats.pending_bookings}</div>
            <div class="text-xs text-white">Pending</div>
        </div>
        <div class="bg-salon-gold rounded-lg p-3 text-center">
            <div class="text-lg font-semibold text-black">TSH ${stats.total_revenue.toFixed(2)}</div>
            <div class="text-xs text-black">Revenue</div>
        </div>
    `;
}

function displayCalendar(events, currentDate) {
    const date = new Date(currentDate);
    const year = date.getFullYear();
    const month = date.getMonth();

    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    let calendarHTML = `
        <div class="grid grid-cols-7 gap-1 mb-2">
            <div class="text-center text-gray-400 font-semibold py-2">Sun</div>
            <div class="text-center text-gray-400 font-semibold py-2">Mon</div>
            <div class="text-center text-gray-400 font-semibold py-2">Tue</div>
            <div class="text-center text-gray-400 font-semibold py-2">Wed</div>
            <div class="text-center text-gray-400 font-semibold py-2">Thu</div>
            <div class="text-center text-gray-400 font-semibold py-2">Fri</div>
            <div class="text-center text-gray-400 font-semibold py-2">Sat</div>
        </div>
        <div class="grid grid-cols-7 gap-1">
    `;

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
        calendarHTML += '<div class="h-24 bg-secondary-600 rounded"></div>';
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const dayDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const dayEvents = events.filter(event => event.start.startsWith(dayDate));
        const isToday = dayDate === new Date().toISOString().split('T')[0];

        calendarHTML += `
            <div class="h-24 bg-secondary-600 rounded p-1 ${isToday ? 'ring-2 ring-salon-gold' : ''}">
                <div class="text-white text-sm font-semibold mb-1">${day}</div>
                <div class="space-y-1">
        `;

        // Add events for this day (max 2 visible)
        dayEvents.slice(0, 2).forEach(event => {
            const startTime = event.start.split('T')[1].substring(0, 5);
            const serviceName = event.extendedProps.service_name || 'Unknown Service';
            const serviceDisplay = serviceName.length > 10 ? serviceName.substring(0, 10) + '...' : serviceName;

            calendarHTML += `
                <div class="text-xs p-1 rounded cursor-pointer hover:opacity-80"
                     style="background-color: ${event.backgroundColor}; color: ${event.textColor};"
                     onclick="showBookingDetail('${event.extendedProps.booking_id}', ${JSON.stringify(event.extendedProps).replace(/"/g, '&quot;')})">
                    ${startTime} ${serviceDisplay}
                </div>
            `;
        });

        // Show "+X more" if there are more events
        if (dayEvents.length > 2) {
            calendarHTML += `
                <div class="text-xs text-gray-300 text-center">
                    +${dayEvents.length - 2} more
                </div>
            `;
        }

        calendarHTML += `
                </div>
            </div>
        `;
    }

    calendarHTML += '</div>';

    document.getElementById('calendarContent').innerHTML = calendarHTML;
}

function changeCalendarMonth(direction) {
    const currentDate = document.getElementById('currentCalendarDate').value;
    const date = new Date(currentDate);
    date.setMonth(date.getMonth() + direction);

    const newDate = date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-01';
    document.getElementById('currentCalendarDate').value = newDate;

    const staffId = document.getElementById('calendarStaffId').value;
    loadCalendarData(staffId, newDate);
}

function goToToday() {
    const today = new Date();
    const currentDate = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-01';
    document.getElementById('currentCalendarDate').value = currentDate;

    const staffId = document.getElementById('calendarStaffId').value;
    loadCalendarData(staffId, currentDate);
}

function showBookingDetail(bookingId, bookingData) {
    const formatTime = (time) => {
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    };

    const statusColors = {
        'PENDING': 'bg-yellow-100 text-yellow-800',
        'CONFIRMED': 'bg-blue-100 text-blue-800',
        'IN_PROGRESS': 'bg-purple-100 text-purple-800',
        'COMPLETED': 'bg-green-100 text-green-800',
        'CANCELLED': 'bg-red-100 text-red-800',
        'NO_SHOW': 'bg-gray-100 text-gray-800'
    };

    document.getElementById('bookingDetailContent').innerHTML = `
        <div class="space-y-4">
            <div>
                <h4 class="font-semibold text-white mb-2">${bookingData.service_type || 'Service'}</h4>
                <p class="text-gray-300">${bookingData.service_name || 'Unknown Service'}</p>
                ${bookingData.is_package ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mt-1">Package</span>' : ''}
            </div>

            <div>
                <h4 class="font-semibold text-white mb-2">Customer</h4>
                <p class="text-gray-300">${bookingData.customer_name || 'Unknown Customer'}</p>
                ${bookingData.customer_email ? `<p class="text-gray-400 text-sm">${bookingData.customer_email}</p>` : ''}
                ${bookingData.customer_phone ? `<p class="text-gray-400 text-sm">${bookingData.customer_phone}</p>` : ''}
            </div>

            <div>
                <h4 class="font-semibold text-white mb-2">Date & Time</h4>
                <p class="text-gray-300">${new Date(bookingData.date).toLocaleDateString()}</p>
                <p class="text-gray-300">${formatTime(bookingData.start_time)} - ${formatTime(bookingData.end_time)}</p>
            </div>

            <div>
                <h4 class="font-semibold text-white mb-2">Status</h4>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[bookingData.status] || 'bg-gray-100 text-gray-800'}">
                    ${bookingData.status}
                </span>
            </div>

            <div>
                <h4 class="font-semibold text-white mb-2">Amount</h4>
                <p class="text-salon-gold font-semibold">TSH ${parseFloat(bookingData.total_amount).toFixed(2)}</p>
            </div>

            ${bookingData.notes ? `
                <div>
                    <h4 class="font-semibold text-white mb-2">Notes</h4>
                    <p class="text-gray-300">${bookingData.notes}</p>
                </div>
            ` : ''}

            <div class="pt-4 border-t border-secondary-600">
                <a href="<?= getBasePath() ?>/admin/bookings/view.php?id=${bookingId}"
                   class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    View Full Details
                </a>
            </div>
        </div>
    `;

    document.getElementById('bookingDetailModal').classList.remove('hidden');
}

function closeBookingDetailModal() {
    document.getElementById('bookingDetailModal').classList.add('hidden');
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
        closeCalendarModal();
        closeBookingDetailModal();
    }
});

// Close modal on backdrop click
document.getElementById('staffModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});



document.getElementById('calendarModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCalendarModal();
    }
});

document.getElementById('bookingDetailModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBookingDetailModal();
    }
});

// Form submission
document.getElementById('staffForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Validate form before submission
    if (!validateStaffForm()) {
        return;
    }

    const formData = new FormData(this);
    const action = formData.get('action');

    // Show loading state
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Saving...';
    submitButton.disabled = true;

    fetch('<?= getBasePath() ?>/admin/staff/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // Reload the page to see changes
        window.location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving the staff member.');

        // Restore button state
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    });
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
