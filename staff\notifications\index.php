<?php
session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Get notifications with pagination and filters
$page = $_GET['page'] ?? 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$category = $_GET['category'] ?? 'all';
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';
$sort = $_GET['sort'] ?? 'newest';

try {
    $notifications = getStaffNotifications($_SESSION['user_id'], [
        'limit' => $limit,
        'offset' => $offset,
        'category' => $category,
        'status' => $status,
        'search' => $search,
        'sort' => $sort
    ]);

    $totalCount = getStaffNotificationsCount($_SESSION['user_id'], [
        'category' => $category,
        'status' => $status,
        'search' => $search
    ]);

    $totalPages = ceil($totalCount / $limit);

    // Get category counts
    $categoryCounts = getNotificationCategoryCounts($_SESSION['user_id']);
} catch (Exception $e) {
    $error = $e->getMessage();
    $notifications = [];
    $totalCount = 0;
    $totalPages = 0;
    $categoryCounts = [
        'total' => 0,
        'unread' => 0,
        'read' => 0,
        'categories' => []
    ];
}

// Helper functions for badge classes
function getCategoryBadgeClass($category) {
    switch ($category) {
        case 'BOOKING':
            return 'bg-blue-500/20 text-blue-400';
        case 'STAFF':
            return 'bg-purple-500/20 text-purple-400';
        case 'SYSTEM':
            return 'bg-gray-500/20 text-gray-400';
        default:
            return 'bg-gray-500/20 text-gray-400';
    }
}

function getPriorityBadgeClass($priority) {
    switch ($priority) {
        case 'HIGH':
            return 'bg-red-500/20 text-red-400';
        case 'MEDIUM':
            return 'bg-yellow-500/20 text-yellow-400';
        case 'LOW':
            return 'bg-green-500/20 text-green-400';
        default:
            return 'bg-gray-500/20 text-gray-400';
    }
}

$pageTitle = "Notifications Management";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Page Header -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 p-8 hover-lift">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-white font-serif">Notification <span class="text-salon-gold">Center</span></h1>
            <p class="mt-2 text-lg text-gray-300">Manage and organize your notifications</p>
        </div>
        <div class="mt-6 sm:mt-0 flex space-x-4">
            <button onclick="markAllAsRead()" class="px-4 py-2 bg-salon-gold text-black rounded-lg hover:bg-gold-light transition-colors">
                Mark All Read
            </button>
            <button onclick="clearAllNotifications()" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                Clear All
            </button>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <!-- Total Notifications -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Total Notifications</dt>
                        <dd class="text-2xl font-bold text-white"><?= number_format($totalCount) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Unread Notifications -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Unread</dt>
                        <dd class="text-2xl font-bold text-red-500"><?= number_format($categoryCounts['unread'] ?? 0) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Read Notifications -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Read</dt>
                        <dd class="text-2xl font-bold text-green-500"><?= number_format($categoryCounts['read'] ?? 0) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Categories</dt>
                        <dd class="text-2xl font-bold text-blue-500"><?= count($categoryCounts['categories'] ?? []) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 p-6">
    <form id="filterForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div class="relative">
            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                   class="w-full bg-secondary-800 border border-secondary-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-salon-gold"
                   placeholder="Search notifications...">
            <button type="submit" class="absolute right-3 top-2.5 text-gray-400 hover:text-white">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </button>
        </div>

        <!-- Category Filter -->
        <div>
            <select name="category" class="w-full bg-secondary-800 border border-secondary-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                <option value="all" <?= $category === 'all' ? 'selected' : '' ?>>All Categories</option>
                <option value="BOOKING" <?= $category === 'BOOKING' ? 'selected' : '' ?>>Bookings</option>
                <option value="STAFF" <?= $category === 'STAFF' ? 'selected' : '' ?>>Staff Updates</option>
                <option value="SYSTEM" <?= $category === 'SYSTEM' ? 'selected' : '' ?>>System</option>
            </select>
        </div>

        <!-- Status Filter -->
        <div>
            <select name="status" class="w-full bg-secondary-800 border border-secondary-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                <option value="unread" <?= $status === 'unread' ? 'selected' : '' ?>>Unread</option>
                <option value="read" <?= $status === 'read' ? 'selected' : '' ?>>Read</option>
            </select>
        </div>

        <!-- Sort -->
        <div>
            <select name="sort" class="w-full bg-secondary-800 border border-secondary-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>Newest First</option>
                <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>Oldest First</option>
                <option value="priority" <?= $sort === 'priority' ? 'selected' : '' ?>>Priority</option>
            </select>
        </div>
    </form>
</div>

<!-- Notifications List -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8">
    <!-- Bulk Actions -->
    <div class="p-4 border-b border-secondary-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <input type="checkbox" id="selectAll" class="rounded border-secondary-700 text-salon-gold focus:ring-salon-gold">
                <label for="selectAll" class="text-sm text-gray-400">Select All</label>
            </div>
            <div class="flex space-x-2">
                <button onclick="bulkMarkAsRead()" class="px-3 py-1 text-sm bg-salon-gold text-black rounded hover:bg-gold-light transition-colors">
                    Mark Selected as Read
                </button>
                <button onclick="bulkDelete()" class="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                    Delete Selected
                </button>
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="divide-y divide-secondary-700">
        <?php if (empty($notifications)): ?>
            <div class="p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-400">No notifications found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your filters or search terms</p>
            </div>
        <?php else: ?>
            <?php foreach ($notifications as $notification): ?>
                <div class="notification-item p-4 hover:bg-secondary-800 transition-colors">
                    <div class="flex items-start space-x-4">
                        <!-- Checkbox -->
                        <div class="flex-shrink-0">
                            <input type="checkbox" value="<?= $notification['id'] ?>" 
                                   class="notification-checkbox rounded border-secondary-700 text-salon-gold focus:ring-salon-gold">
                        </div>

                        <!-- Notification Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getCategoryBadgeClass($notification['category']) ?>">
                                        <?= $notification['category'] ?>
                                    </span>
                                    <?php if ($notification['priority'] !== 'MEDIUM'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getPriorityBadgeClass($notification['priority']) ?>">
                                            <?= $notification['priority'] ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-400"><?= $notification['time_ago'] ?></span>
                                    <div class="flex space-x-1">
                                        <?php if (!$notification['is_read']): ?>
                                            <button onclick="markAsRead('<?= $notification['id'] ?>')" 
                                                    class="p-1 text-gray-400 hover:text-white transition-colors" 
                                                    title="Mark as read">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </button>
                                        <?php endif; ?>
                                        <button onclick="deleteNotification('<?= $notification['id'] ?>')" 
                                                class="p-1 text-gray-400 hover:text-red-500 transition-colors" 
                                                title="Delete">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <h3 class="mt-1 text-sm font-medium text-white"><?= htmlspecialchars($notification['title']) ?></h3>
                            <p class="mt-1 text-sm text-gray-300"><?= htmlspecialchars($notification['message']) ?></p>
                            <?php if ($notification['action_url']): ?>
                                <div class="mt-2">
                                    <a href="<?= getBasePath() . $notification['action_url'] ?>" 
                                       class="text-sm text-salon-gold hover:text-gold-light transition-colors">
                                        View Details →
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div class="px-4 py-3 border-t border-secondary-700">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                           class="relative inline-flex items-center px-4 py-2 border border-secondary-700 text-sm font-medium rounded-md text-gray-400 bg-secondary-800 hover:bg-secondary-700">
                            Previous
                        </a>
                    <?php endif; ?>
                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?= $page + 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                           class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-700 text-sm font-medium rounded-md text-gray-400 bg-secondary-800 hover:bg-secondary-700">
                            Next
                        </a>
                    <?php endif; ?>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-400">
                            Showing <span class="font-medium"><?= ($offset + 1) ?></span> to 
                            <span class="font-medium"><?= min($offset + $limit, $totalCount) ?></span> of 
                            <span class="font-medium"><?= $totalCount ?></span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-700 bg-secondary-800 text-sm font-medium text-gray-400 hover:bg-secondary-700">
                                    <span class="sr-only">Previous</span>
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                    </svg>
                                </a>
                            <?php endif; ?>

                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                                <a href="?page=<?= $i ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-700 text-sm font-medium <?= $i === $page ? 'bg-salon-gold text-black' : 'bg-secondary-800 text-gray-400 hover:bg-secondary-700' ?>">
                                    <?= $i ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-700 bg-secondary-800 text-sm font-medium text-gray-400 hover:bg-secondary-700">
                                    <span class="sr-only">Next</span>
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Notification Details Modal -->
<div id="notificationModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-secondary-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="modal-title">
                            Notification Details
                        </h3>
                        <div class="mt-4">
                            <div id="modalContent" class="text-sm text-gray-300"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-secondary-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-salon-gold text-base font-medium text-black hover:bg-gold-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Notification Management Functions
function markAsRead(notificationId) {
    const basePath = '<?= getBasePath() ?>';
    
    makeRequest(`${basePath}/api/staff/notifications.php`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            id: notificationId,
            is_read: true
        })
    })
    .then(() => {
        showNotification('Notification marked as read', 'success');
        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to mark notification as read:', error);
        showNotification('Failed to mark notification as read', 'error');
    });
}

function deleteNotification(notificationId) {
    if (!confirm('Are you sure you want to delete this notification?')) {
        return;
    }

    const basePath = '<?= getBasePath() ?>';
    
    makeRequest(`${basePath}/api/staff/notifications.php?id=${notificationId}`, {
        method: 'DELETE'
    })
    .then(() => {
        showNotification('Notification deleted', 'success');
        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to delete notification:', error);
        showNotification('Failed to delete notification', 'error');
    });
}

function bulkMarkAsRead() {
    const selectedIds = getSelectedNotificationIds();
    if (selectedIds.length === 0) {
        showNotification('Please select notifications to mark as read', 'info');
        return;
    }

    const basePath = '<?= getBasePath() ?>';
    
    Promise.all(selectedIds.map(id =>
        makeRequest(`${basePath}/api/staff/notifications.php`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: id,
                is_read: true
            })
        })
    ))
    .then(() => {
        showNotification('Selected notifications marked as read', 'success');
        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to mark notifications as read:', error);
        showNotification('Failed to mark notifications as read', 'error');
    });
}

function bulkDelete() {
    const selectedIds = getSelectedNotificationIds();
    if (selectedIds.length === 0) {
        showNotification('Please select notifications to delete', 'info');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedIds.length} notification(s)?`)) {
        return;
    }

    const basePath = '<?= getBasePath() ?>';
    
    Promise.all(selectedIds.map(id =>
        makeRequest(`${basePath}/api/staff/notifications.php?id=${id}`, {
            method: 'DELETE'
        })
    ))
    .then(() => {
        showNotification('Selected notifications deleted', 'success');
        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to delete notifications:', error);
        showNotification('Failed to delete notifications', 'error');
    });
}

function getSelectedNotificationIds() {
    return Array.from(document.querySelectorAll('.notification-checkbox:checked'))
        .map(checkbox => checkbox.value);
}

// Select All Checkbox
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.notification-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});

// Filter Form
document.getElementById('filterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const params = new URLSearchParams(formData);
    window.location.href = '?' + params.toString();
});

// Modal Functions
function showModal(content) {
    const modal = document.getElementById('notificationModal');
    const modalContent = document.getElementById('modalContent');
    modalContent.innerHTML = content;
    modal.classList.remove('hidden');
}

function closeModal() {
    const modal = document.getElementById('notificationModal');
    modal.classList.add('hidden');
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Close modal when clicking outside
    document.getElementById('notificationModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Close modal when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
});

// Function to mark all notifications as read
function markAllAsRead() {
    if (!confirm('Are you sure you want to mark all notifications as read?')) return;
    
    fetch('<?= getBasePath() ?>/api/staff/notifications.php', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ mark_all_read: true })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(data.error);
            return;
        }
        // Reload the page to show updated status
        window.location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to mark notifications as read');
    });
}

// Function to clear all notifications
function clearAllNotifications() {
    if (!confirm('Are you sure you want to delete all notifications? This action cannot be undone.')) return;
    
    fetch('<?= getBasePath() ?>/api/staff/notifications.php', {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(data.error);
            return;
        }
        // Reload the page to show updated status
        window.location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to delete notifications');
    });
}
</script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?> 