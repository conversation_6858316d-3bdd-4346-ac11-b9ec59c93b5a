<?php
/**
 * Database Seeder
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../config/app.php';

echo "Starting database seeding...\n";

try {
    // Create demo users
    echo "Creating demo users...\n";
    
    // Admin user
    $adminPassword = password_hash('admin123', PASSWORD_BCRYPT);
    $database->query(
        "INSERT INTO users (id, name, email, password, role, points, referral_code) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [generateUUID(), 'Admin User', '<EMAIL>', $adminPassword, 'ADMIN', 0, 'ADM001']
    );
    
    // Staff user
    $staffPassword = password_hash('staff123', PASSWORD_BCRYPT);
    $database->query(
        "INSERT INTO users (id, name, email, password, role, points, referral_code) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [generateUUID(), 'Staff Member', '<EMAIL>', $staffPassword, 'STAFF', 0, 'STF001']
    );

    // Customer user
    $customerPassword = password_hash('customer123', PASSWORD_BCRYPT);
    $database->query(
        "INSERT INTO users (id, name, email, password, role, points, referral_code) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [generateUUID(), 'John Customer', '<EMAIL>', $customerPassword, 'CUSTOMER', 150, 'JOH001']
    );
    
    // Additional customers
    $customers = [
        ['Sarah Johnson', '<EMAIL>', 'SAR001'],
        ['Mike Wilson', '<EMAIL>', 'MIK001'],
        ['Emma Davis', '<EMAIL>', 'EMM001'],
        ['David Brown', '<EMAIL>', 'DAV001'],
        ['Lisa Garcia', '<EMAIL>', 'LIS001']
    ];
    
    foreach ($customers as $customer) {
        $password = password_hash('password123', PASSWORD_BCRYPT);
        $database->query(
            "INSERT INTO users (id, name, email, password, role, points, referral_code) VALUES (?, ?, ?, ?, ?, ?, ?)",
            [generateUUID(), $customer[0], $customer[1], $password, 'CUSTOMER', rand(50, 300), $customer[2]]
        );
    }
    
    // Create service categories first
    echo "Creating service categories...\n";

    $serviceCategories = [
        ['Hair', 'Professional hair cutting, styling, and treatment services'],
        ['Facial', 'Rejuvenating facial treatments and skincare services'],
        ['Nails', 'Complete nail care including manicure and pedicure services'],
        ['Massage', 'Relaxing therapeutic massage and wellness services'],
        ['Beauty', 'Comprehensive beauty treatments and cosmetic services'],
        ['Spa', 'Luxury spa treatments and relaxation services']
    ];

    foreach ($serviceCategories as $category) {
        $database->query(
            "INSERT INTO service_categories (id, name, description, is_active) VALUES (?, ?, ?, 1)",
            [generateUUID(), $category[0], $category[1]]
        );
    }

    // Create services
    echo "Creating services...\n";
    
    $services = [
        ['Hair Cut & Style', 'Professional haircut with styling', 45.00, 60, 'Hair', 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400'],
        ['Hair Coloring', 'Full hair coloring service', 85.00, 120, 'Hair', 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400'],
        ['Hair Highlights', 'Professional highlighting service', 95.00, 150, 'Hair', 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400'],
        ['Facial Treatment', 'Deep cleansing facial treatment', 65.00, 75, 'Facial', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400'],
        ['Anti-Aging Facial', 'Advanced anti-aging facial', 95.00, 90, 'Facial', 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400'],
        ['Manicure', 'Classic manicure service', 35.00, 45, 'Nails', 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400'],
        ['Pedicure', 'Relaxing pedicure service', 45.00, 60, 'Nails', 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400'],
        ['Gel Manicure', 'Long-lasting gel manicure', 55.00, 60, 'Nails', 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400'],
        ['Swedish Massage', 'Relaxing full body massage', 80.00, 60, 'Massage', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400'],
        ['Deep Tissue Massage', 'Therapeutic deep tissue massage', 95.00, 75, 'Massage', 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400'],
        ['Eyebrow Shaping', 'Professional eyebrow shaping', 25.00, 30, 'Beauty', 'https://images.unsplash.com/photo-1588681664899-f142ff2dc9b1?w=400'],
        ['Makeup Application', 'Professional makeup service', 75.00, 60, 'Beauty', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400']
    ];
    
    foreach ($services as $service) {
        $database->query(
            "INSERT INTO services (id, name, description, price, duration, category, image) VALUES (?, ?, ?, ?, ?, ?, ?)",
            [generateUUID(), $service[0], $service[1], $service[2], $service[3], $service[4], $service[5]]
        );
    }
    
    // Create staff members
    echo "Creating staff members...\n";
    
    $staffMembers = [
        ['Sarah Williams', '<EMAIL>', '+**********', 'Senior Hair Stylist', '["Hair Cut", "Hair Coloring", "Hair Styling"]', 25.00, 'Professional hair stylist with 8 years of experience', 8],
        ['Michael Chen', '<EMAIL>', '+**********', 'Massage Therapist', '["Swedish Massage", "Deep Tissue Massage", "Hot Stone Massage"]', 30.00, 'Licensed massage therapist specializing in therapeutic treatments', 6],
        ['Emily Rodriguez', '<EMAIL>', '+**********', 'Esthetician', '["Facial Treatment", "Anti-Aging Facial", "Chemical Peels"]', 28.00, 'Certified esthetician with expertise in skincare', 5],
        ['Jessica Taylor', '<EMAIL>', '+**********', 'Nail Technician', '["Manicure", "Pedicure", "Gel Nails", "Nail Art"]', 20.00, 'Creative nail artist with attention to detail', 4],
        ['Amanda Johnson', '<EMAIL>', '+**********', 'Makeup Artist', '["Makeup Application", "Bridal Makeup", "Special Events"]', 35.00, 'Professional makeup artist for all occasions', 7]
    ];
    
    foreach ($staffMembers as $staff) {
        $schedule = json_encode([
            'monday' => ['start' => '09:00', 'end' => '17:00'],
            'tuesday' => ['start' => '09:00', 'end' => '17:00'],
            'wednesday' => ['start' => '09:00', 'end' => '17:00'],
            'thursday' => ['start' => '09:00', 'end' => '17:00'],
            'friday' => ['start' => '09:00', 'end' => '17:00'],
            'saturday' => ['start' => '10:00', 'end' => '16:00'],
            'sunday' => ['start' => 'closed', 'end' => 'closed']
        ]);
        
        $database->query(
            "INSERT INTO staff (id, name, email, phone, role, specialties, hourly_rate, bio, experience, schedule) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [generateUUID(), $staff[0], $staff[1], $staff[2], $staff[3], $staff[4], $staff[5], $staff[6], $staff[7], $schedule]
        );
    }
    
    // Create packages
    echo "Creating packages...\n";
    
    $packages = [
        ['Bridal Beauty Package', 'Complete bridal beauty treatment including hair, makeup, and nails', 250.00, 15.00, 'https://images.unsplash.com/photo-1519741497674-************?w=400'],
        ['Spa Day Package', 'Relaxing spa day with facial, massage, and nail treatment', 180.00, 20.00, 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400'],
        ['Hair Transformation', 'Complete hair makeover with cut, color, and styling', 150.00, 10.00, 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400']
    ];
    
    foreach ($packages as $package) {
        $database->query(
            "INSERT INTO packages (id, name, description, price, discount, image) VALUES (?, ?, ?, ?, ?, ?)",
            [generateUUID(), $package[0], $package[1], $package[2], $package[3], $package[4]]
        );
    }
    
    // Create offers
    echo "Creating offers...\n";
    
    $offers = [
        ['New Customer Special', 'Get 20% off your first visit', 20.00, 'WELCOME20', date('Y-m-d H:i:s'), date('Y-m-d H:i:s', strtotime('+3 months')), 100, 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400'],
        ['Summer Glow Package', 'Special summer facial and hair treatment combo', 25.00, 'SUMMER25', date('Y-m-d H:i:s'), date('Y-m-d H:i:s', strtotime('+2 months')), 50, 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400'],
        ['Referral Bonus', 'Refer a friend and both get 15% off', 15.00, 'REFER15', date('Y-m-d H:i:s'), date('Y-m-d H:i:s', strtotime('+6 months')), null, 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400']
    ];
    
    foreach ($offers as $offer) {
        $database->query(
            "INSERT INTO offers (id, title, description, discount, code, valid_from, valid_to, max_usage, image) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [generateUUID(), $offer[0], $offer[1], $offer[2], $offer[3], $offer[4], $offer[5], $offer[6], $offer[7]]
        );
    }
    
    // Create sample bookings
    echo "Creating sample bookings...\n";
    
    $customerIds = $database->fetchAll("SELECT id FROM users WHERE role = 'CUSTOMER'");
    $serviceIds = $database->fetchAll("SELECT id FROM services");
    $staffIds = $database->fetchAll("SELECT id FROM staff");
    
    for ($i = 0; $i < 20; $i++) {
        $customerId = $customerIds[array_rand($customerIds)]['id'];
        $serviceId = $serviceIds[array_rand($serviceIds)]['id'];
        $staffId = $staffIds[array_rand($staffIds)]['id'];
        
        $date = date('Y-m-d', strtotime('+' . rand(1, 30) . ' days'));
        $startTime = sprintf('%02d:00', rand(9, 16));
        $endTime = sprintf('%02d:00', rand(10, 17));
        $status = ['PENDING', 'CONFIRMED', 'COMPLETED'][rand(0, 2)];
        $amount = rand(50, 200);
        
        $database->query(
            "INSERT INTO bookings (id, user_id, service_id, staff_id, date, start_time, end_time, status, total_amount, points_earned) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [generateUUID(), $customerId, $serviceId, $staffId, $date, $startTime, $endTime, $status, $amount, floor($amount * 0.1)]
        );
    }
    
    // Create gallery images
    echo "Creating gallery images...\n";
    
    $galleryImages = [
        ['Hair Styling Work', 'Beautiful hair transformation', 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=600', 'Hair'],
        ['Facial Treatment', 'Relaxing facial session', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=600', 'Facial'],
        ['Nail Art Design', 'Creative nail art', 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=600', 'Nails'],
        ['Massage Therapy', 'Therapeutic massage session', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=600', 'Massage'],
        ['Hair Coloring', 'Professional hair coloring', 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600', 'Hair'],
        ['Makeup Application', 'Professional makeup', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=600', 'Beauty'],
        ['Salon Interior', 'Modern salon space', 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=600', 'Interior'],
        ['Beauty Treatment', 'Luxury beauty service', 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=600', 'Beauty']
    ];
    
    foreach ($galleryImages as $image) {
        $database->query(
            "INSERT INTO gallery (id, title, description, image_url, category) VALUES (?, ?, ?, ?, ?)",
            [generateUUID(), $image[0], $image[1], $image[2], $image[3]]
        );
    }
    
    // Create CMS content
    echo "Creating CMS content...\n";
    
    $cmsContent = [
        ['hero', 'Welcome to Flix Salonce', 'Experience luxury and elegance at our premium beauty salon', null, 1],
        ['about', 'About Our Salon', 'We are dedicated to providing exceptional beauty services in a luxurious environment', null, 1],
        ['mission', 'Our Mission', 'To enhance natural beauty and boost confidence through professional beauty services', null, 1]
    ];
    
    foreach ($cmsContent as $content) {
        $database->query(
            "INSERT INTO cms_content (id, section, title, content, order_index) VALUES (?, ?, ?, ?, ?)",
            [generateUUID(), $content[0], $content[1], $content[2], $content[4]]
        );
    }
    
    // Create settings
    echo "Creating system settings...\n";
    
    $settings = [
        ['business', 'name', 'Flix Salon & SPA'],
        ['business', 'address', 'Upanga, Dar Es Salaam, Tanzania'],
        ['business', 'phone', '(255) 745 456-789'],
        ['business', 'email', '<EMAIL>'],
        ['business', 'hours', 'Mon-Sat: 9AM-7PM, Sun: 10AM-6PM'],
        ['booking', 'advance_days', '30'],
        ['booking', 'cancellation_hours', '24'],
        ['points', 'earn_rate', '1'],
        ['points', 'redeem_rate', '100'],
        ['email', 'notifications', '1'],
        ['payment', 'stripe_enabled', '1']
    ];
    
    foreach ($settings as $setting) {
        $database->query(
            "INSERT INTO settings (id, category, setting_key, setting_value) VALUES (?, ?, ?, ?)",
            [generateUUID(), $setting[0], $setting[1], $setting[2]]
        );
    }
    
    echo "Database seeding completed successfully!\n";
    echo "\nDemo accounts created:\n";
    echo "Admin: <EMAIL> / admin123\n";
    echo "Staff: <EMAIL> / staff123\n";
    echo "Customer: <EMAIL> / customer123\n";
    
} catch (Exception $e) {
    echo "Error seeding database: " . $e->getMessage() . "\n";
}
?>
